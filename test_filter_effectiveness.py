#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试过滤效果
验证调试信息是否正确过滤
"""

import sys
import os
import tkinter as tk
import tkinter.ttk as ttk
import re
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'qmt_ths'))

def test_filter_effectiveness():
    """测试过滤效果"""
    print("🧪 测试过滤效果")
    print("=" * 50)
    
    try:
        # 创建测试GUI
        root = tk.Tk()
        root.title("过滤效果测试")
        root.geometry("1000x800")
        
        # 创建Notebook用于分页显示
        log_notebook = ttk.Notebook(root)
        log_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 简化日志页面（前台）
        simplified_frame = ttk.Frame(log_notebook)
        log_notebook.add(simplified_frame, text="交易日志（前台）- 应该简洁")
        
        simplified_log_text = tk.Text(simplified_frame, height=15, width=100)
        simplified_log_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        simplified_scrollbar = ttk.Scrollbar(simplified_frame, orient=tk.VERTICAL, command=simplified_log_text.yview)
        simplified_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        simplified_log_text['yscrollcommand'] = simplified_scrollbar.set
        
        # 详细日志页面（后台）
        detailed_frame = ttk.Frame(log_notebook)
        log_notebook.add(detailed_frame, text="详细日志（后台）- 显示所有信息")
        
        detailed_log_text = tk.Text(detailed_frame, height=15, width=100)
        detailed_log_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        detailed_scrollbar = ttk.Scrollbar(detailed_frame, orient=tk.VERTICAL, command=detailed_log_text.yview)
        detailed_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        detailed_log_text['yscrollcommand'] = detailed_scrollbar.set
        
        # 模拟修复后的过滤系统
        class FilterTestSystem:
            def __init__(self):
                self.root = root
                self.simplified_log_text = simplified_log_text
                self.detailed_log_text = detailed_log_text
                self.enhanced_log_manager = None
                
                # 前台简化日志功能 - 股票信息去重
                self._displayed_stocks = set()
                self._last_stock_messages = {}
            
            def _should_filter_from_simplified_log(self, message):
                """修复后的过滤逻辑"""
                import re
                
                # 检查是否是股票交易信息（重要信息，需要显示）
                stock_pattern = r'\d{6}\([^)]+\):'
                if re.search(stock_pattern, message):
                    # 提取股票代码
                    stock_match = re.search(r'(\d{6})\([^)]+\):', message)
                    if stock_match:
                        stock_code = stock_match.group(1)
                        # 检查是否是新的股票信息或状态变化
                        if stock_code not in self._last_stock_messages or self._last_stock_messages[stock_code] != message:
                            self._last_stock_messages[stock_code] = message
                            return False  # 显示新的或变化的股票信息
                        else:
                            return True   # 过滤重复的股票信息
                
                # 过滤掉过于详细的调试信息
                filter_patterns = [
                    # 行情获取调试信息
                    "获取.*行情数据",
                    "详细行情获取调试",
                    "准备尝试的代码格式",
                    "尝试获取代码",
                    "获取到行情数据",
                    
                    # 价格计算调试信息
                    "买入价格计算:",
                    "卖出价格计算:",
                    "价格类型:",
                    "价格调整:",
                    
                    # 五档行情信息
                    ".*五档行情:",
                    "卖五:",
                    "卖四:",
                    "卖三:",
                    "卖二:",
                    "卖一:",
                    "最新:",
                    "买一:",
                    "买二:",
                    "买三:",
                    "买四:",
                    "买五:",
                    "五档无合适价格",
                    "转限价委托:",
                    
                    # 涨跌停价格信息
                    ".*从接口获取涨跌停价格:",
                    "涨停价=",
                    "跌停价=",
                    "最新价:",
                    "涨停价:",
                    "跌停价:",
                    "买入价格.*超过涨停价",
                    "卖出价格.*低于跌停价",
                    "调整为涨停价",
                    "调整为跌停价",
                    "最终委托价格:",
                    
                    # 监控任务信息
                    "监控任务执行",
                    "上一个监控任务仍在执行中",
                    "定时任务执行完成",
                    "监控板块.*中的.*只股票",
                    
                    # 委托回报信息
                    "委托回报推送:",
                    
                    # 其他调试信息
                    "完整数据:",
                    ".*最新价格:",
                    "当前持仓市值:",
                    "预计买入后市值:",
                    "目标总金额:",
                    "剩余差额:",
                    "预计在.*后继续买入",
                    "直到达到目标金额"
                ]

                for pattern in filter_patterns:
                    if re.search(pattern, message):
                        return True
                return False
            
            def _safe_log_message(self, message, level="INFO"):
                """修复后的安全日志方法"""
                try:
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    detailed_message = f"[{current_time}] [{level}] {message}"

                    # 检查GUI组件是否可用
                    gui_available = (
                        hasattr(self, 'root') and self.root and 
                        hasattr(self, 'simplified_log_text') and 
                        hasattr(self, 'detailed_log_text')
                    )
                    
                    if not gui_available:
                        print(f"[{level}] {message} (GUI不可用)")
                        return

                    # 更新详细日志（完整格式）
                    try:
                        if self.detailed_log_text and self.detailed_log_text.winfo_exists():
                            self.detailed_log_text.insert(tk.END, f"{detailed_message}\n")
                            self.detailed_log_text.see(tk.END)
                    except tk.TclError:
                        pass

                    # 更新简化日志（简化格式，过滤调试信息）
                    try:
                        if self.simplified_log_text and self.simplified_log_text.winfo_exists():
                            if not self._should_filter_from_simplified_log(message):
                                simple_time = datetime.now().strftime("%H:%M:%S")
                                simple_message = f"[{simple_time}] {message}"
                                self.simplified_log_text.insert(tk.END, f"{simple_message}\n")
                                self.simplified_log_text.see(tk.END)
                    except tk.TclError:
                        pass

                except Exception as e:
                    try:
                        print(f"[{level}] {message}")
                        print(f"GUI日志记录失败: {e}")
                    except:
                        pass
            
            def log_message(self, message, level="INFO"):
                """修复后的log_message方法"""
                try:
                    # 优先使用增强日志管理器
                    if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                        try:
                            self.enhanced_log_manager.log_message(message, level)
                            return
                        except Exception as e:
                            print(f"增强日志管理器失败: {e}")
                    
                    # 回退到安全日志方法
                    self._safe_log_message(message, level)
                    
                except Exception as e:
                    try:
                        print(f"[{level}] {message}")
                        print(f"日志记录失败: {e}")
                    except:
                        pass
        
        # 创建过滤测试系统
        filter_system = FilterTestSystem()
        
        # 测试用的真实日志消息
        test_messages = [
            # 应该显示在前台的重要信息
            ("配置加载成功", "INFO"),
            ("开始交易 - 账户: 40108561, 板块: 涨停双响炮刚启动", "INFO"),
            ("交易系统启动成功", "INFO"),
            ("300689(澄天伟业): 56.72元涨停封板 无卖盘 委托价56.72", "INFO"),
            ("买入委托成功: 301076.SZ, 数量: 500, 价格: 37.15, 订单号: 1098937267", "INFO"),
            ("⚠️ 301123.SZ单笔金额不足: 需要3079.00元买入100股，当前可用1526.00元", "WARNING"),
            ("💡 建议: 将单笔金额设置为至少3079元，或选择价格更低的股票", "INFO"),
            
            # 应该被过滤的调试信息
            ("301076.SZ 买入价格计算:", "DEBUG"),
            ("   价格类型: 最优五档转限价", "DEBUG"),
            ("   价格调整: 0.5%", "DEBUG"),
            ("📊 301076.SZ 五档行情:", "DEBUG"),
            ("   卖五: 0.000 (0)", "DEBUG"),
            ("   卖四: 0.000 (0)", "DEBUG"),
            ("   卖三: 0.000 (0)", "DEBUG"),
            ("   卖二: 0.000 (0)", "DEBUG"),
            ("   卖一: 0.000 (0)", "DEBUG"),
            ("   最新: 37.150", "DEBUG"),
            ("   买一: 37.150 (0)", "DEBUG"),
            ("   五档无合适价格，转限价委托: 37.336", "DEBUG"),
            ("📊 301076.SZ从接口获取涨跌停价格: 涨停价=37.15, 跌停价=24.77", "DEBUG"),
            ("⚠️ 301076.SZ买入价格37.336超过涨停价37.15，调整为涨停价", "DEBUG"),
            ("   最终委托价格: 37.15", "DEBUG"),
            ("委托回报推送: {'证券代码': '301076.SZ', '委托状态': 50, '系统编号': '25566'}", "DEBUG"),
            ("当前持仓市值: 0.00, 预计买入后市值: 18575.00, 目标总金额: 20000.00, 剩余差额: 1425.00", "DEBUG"),
            ("监控板块 涨停双响炮刚启动 中的 11 只股票", "DEBUG"),
        ]
        
        # 测试函数
        def test_all_messages():
            """测试所有消息"""
            for message, level in test_messages:
                filter_system.log_message(message, level)
        
        def test_important_only():
            """只测试重要消息"""
            important_messages = [
                ("配置加载成功", "INFO"),
                ("交易系统启动成功", "INFO"),
                ("300689(澄天伟业): 56.72元涨停封板 无卖盘 委托价56.72", "INFO"),
                ("买入委托成功: 301076.SZ, 数量: 500, 价格: 37.15", "INFO"),
            ]
            for message, level in important_messages:
                filter_system.log_message(message, level)
        
        def test_debug_only():
            """只测试调试消息（应该被过滤）"""
            debug_messages = [
                ("301076.SZ 买入价格计算:", "DEBUG"),
                ("📊 301076.SZ 五档行情:", "DEBUG"),
                ("   卖五: 0.000 (0)", "DEBUG"),
                ("   最新: 37.150", "DEBUG"),
                ("委托回报推送: {'证券代码': '301076.SZ'}", "DEBUG"),
            ]
            for message, level in debug_messages:
                filter_system.log_message(message, level)
        
        def clear_logs():
            """清空日志"""
            simplified_log_text.delete(1.0, tk.END)
            detailed_log_text.delete(1.0, tk.END)
        
        # 创建按钮
        button_frame = tk.Frame(root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="测试所有消息", command=test_all_messages).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="只测试重要消息", command=test_important_only).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="只测试调试消息", command=test_debug_only).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空日志", command=clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 添加说明
        info_text = """
测试说明：
1. 前台日志应该只显示重要信息：配置、交易结果、股票状态、错误警告
2. 后台日志显示所有信息：包括调试信息、五档行情、价格计算等
3. 调试消息（📊、委托回报等）应该只在后台显示
4. 点击"只测试调试消息"，前台应该没有任何新内容
        """
        info_label = tk.Label(root, text=info_text, justify=tk.LEFT, anchor="w")
        info_label.pack(fill=tk.X, padx=10, pady=5)
        
        # 初始化测试
        filter_system.log_message("过滤测试程序启动", "INFO")
        
        print("✅ 过滤测试GUI创建成功")
        print("📋 测试重点：")
        print("1. 前台是否只显示重要信息")
        print("2. 调试信息是否被正确过滤")
        print("3. 后台是否显示所有信息")
        
        # 运行GUI
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚨 过滤效果测试")
    print("=" * 60)
    print("验证调试信息是否正确过滤")
    print("=" * 60)
    
    # 功能测试
    functionality_result = test_filter_effectiveness()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print("=" * 60)
    
    print(f"功能测试: {'✅ 通过' if functionality_result else '❌ 失败'}")
    
    if functionality_result:
        print(f"\n🎉 过滤测试成功！")
        print("✅ 过滤规则已大幅增强")
        print("✅ 调试信息应该被正确过滤")
        print("✅ 前台日志应该更加简洁")
        print("✅ 后台日志保持完整")
        print("\n💡 新增过滤规则：")
        print("• 价格计算调试信息")
        print("• 五档行情详细信息")
        print("• 涨跌停价格信息")
        print("• 委托回报推送信息")
        print("• 持仓市值计算信息")
    else:
        print(f"\n❌ 测试失败，可能需要进一步检查")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
