import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime, timedelta, timezone
import threading
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtdata
from xtquant import xtconstant
import random
import time
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
import re
import traceback
import logging

# 导入窗口管理器
from window_manager import WindowManager

# 导入增强日志管理器
from enhanced_log_manager import EnhancedLogManager

# 配置日志系统
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('trading_system')

# 导入同花顺板块读取模块
# 从当前目录导入read_block模块
try:
    from read_block import get_stocks_by_block_name
except ModuleNotFoundError:
    # 如果直接导入失败，尝试使用相对导入
    import os
    import sys
    # 获取当前脚本所在的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 将当前目录添加到Python的搜索路径中
    if current_dir not in sys.path:
        sys.path.append(current_dir)
    # 再次尝试导入
    from read_block import get_stocks_by_block_name


# 在类定义之前添加回调类
class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def __init__(self, logger):
        super().__init__()
        self.logger = logger  # 保存日志记录器的引用

    def on_stock_order(self, order):
        """委托回报推送"""
        order_info = {
            '证券代码': order.stock_code,
            '委托状态': order.order_status,
            '系统编号': order.order_sysid
        }
        self.logger(f"委托回报推送: {order_info}")

    def on_stock_trade(self, trade):
        """成交变动推送"""
        try:
            buy_sell = 1 if trade.order_type == 23 else -1 if trade.order_type == 24 else 0
            trade_info = {
                '资金账号': trade.account_id,
                '订单编号': trade.order_id,
                '策略名称': trade.strategy_name,
                '委托备注': trade.order_remark,
                '成交时间': self.convert_time(trade.traded_time),
                '委托类型': trade.order_type,
                '证券代码': trade.stock_code,
                '成交均价': trade.traded_price,
                '成交数量': trade.traded_volume,
                '成交金额': trade.traded_amount,
                '买卖': buy_sell
            }
            self.logger(f"成交变动推送: {trade_info}")
        except Exception as e:
            self.logger(f"处理成交推送出错: {str(e)}")

    def on_order_error(self, order_error):
        """委托失败推送 - 优化显示格式"""
        # 🔧 修复：检查系统是否正在停止，避免停止后的回调处理
        if getattr(self, 'is_system_stopping', False):
            return  # 系统正在停止，忽略委托失败回调

        # 🔧 优化：创建用户友好的委托失败信息
        user_friendly_message = self._format_order_error_message(
            order_error.order_id,
            order_error.error_id,
            order_error.error_msg
        )

        # 简洁的用户日志
        self.logger(user_friendly_message)

        # 详细的技术日志
        detailed_error_info = {
            '订单编号': order_error.order_id,
            '错误代码': order_error.error_id,
            '错误信息': order_error.error_msg
        }
        self._log_detailed_error(f"委托失败详细信息: {detailed_error_info}")

    def _format_order_error_message(self, order_id, error_code, error_msg):
        """
        🔧 格式化委托失败信息为用户友好格式
        """
        try:
            # 委托失败错误代码映射表
            order_error_mapping = {
                11000001: "资金不足",
                11000002: "股票代码无效",
                11000003: "委托数量无效",
                11000004: "委托价格无效",
                11000005: "超出涨跌停限制",
                11000006: "非交易时间",
                11000007: "账户状态异常",
                11000008: "持仓不足",
                11000009: "委托类型错误",
                11000010: "系统繁忙"
            }

            # 获取用户友好的错误描述
            user_friendly_desc = order_error_mapping.get(error_code, "委托失败")

            # 清理原始错误信息
            cleaned_msg = self._clean_error_message(error_msg)

            # 选择最合适的描述
            if cleaned_msg and len(cleaned_msg) > 2:
                final_desc = cleaned_msg
            else:
                final_desc = user_friendly_desc

            # 返回简洁格式
            return f"委托失败: 订单{order_id} - {final_desc}"

        except Exception:
            return f"委托失败: 订单{order_id} - 处理异常"

    def on_cancel_error(self, cancel_error):
        """撤单失败推送 - 优化显示格式"""
        # 🔧 修复：检查系统是否正在停止，避免停止后的回调处理
        if getattr(self, 'is_system_stopping', False):
            return  # 系统正在停止，忽略撤单失败回调

        # 🔧 优化：创建用户友好的撤单失败信息
        user_friendly_message = self._format_cancel_error_message(
            cancel_error.order_id,
            cancel_error.error_id,
            cancel_error.error_msg
        )

        # 简洁的用户日志
        self.logger(user_friendly_message)

        # 详细的技术日志（保存到详细日志中）
        detailed_error_info = {
            '订单编号': cancel_error.order_id,
            '错误代码': cancel_error.error_id,
            '错误信息': cancel_error.error_msg
        }
        self._log_detailed_error(f"撤单失败详细信息: {detailed_error_info}")

    def _format_cancel_error_message(self, order_id, error_code, error_msg):
        """
        🔧 格式化撤单失败信息为用户友好格式
        将技术错误信息转换为简洁易懂的描述
        """
        try:
            # 错误代码映射表
            error_code_mapping = {
                251013: "订单已撤销",
                251014: "订单不存在",
                251015: "订单已成交",
                251016: "订单状态异常",
                251017: "撤单权限不足",
                251018: "系统繁忙",
                251019: "网络连接异常",
                251020: "账户状态异常"
            }

            # 获取用户友好的错误描述
            user_friendly_desc = error_code_mapping.get(error_code, "撤单失败")

            # 清理原始错误信息中的技术细节
            cleaned_msg = self._clean_error_message(error_msg)

            # 如果清理后的消息有有用信息，则使用它；否则使用映射的描述
            if cleaned_msg and cleaned_msg != error_msg:
                final_desc = cleaned_msg
            else:
                final_desc = user_friendly_desc

            # 返回简洁格式
            return f"撤单失败: 订单{order_id} - {final_desc}"

        except Exception as e:
            # 如果格式化失败，返回基本信息
            return f"撤单失败: 订单{order_id} - 处理异常"

    def _clean_error_message(self, error_msg):
        """
        清理错误信息中的技术细节
        移除 [COUNTER]、[char_config_xxx]、换行符等技术信息
        """
        try:
            if not error_msg:
                return ""

            # 移除常见的技术标记
            cleaned = error_msg

            # 移除 [COUNTER] 标记
            cleaned = re.sub(r'\[COUNTER\]\s*', '', cleaned)

            # 移除 [数字] 标记
            cleaned = re.sub(r'\[\d+\]', '', cleaned)

            # 移除 [char_config_xxx] 标记
            cleaned = re.sub(r'\[char_config_[^\]]*\]', '', cleaned)

            # 移除多余的换行符和空白字符
            cleaned = re.sub(r'[\r\n]+', ' ', cleaned)
            cleaned = re.sub(r'\s+', ' ', cleaned)

            # 移除首尾空白
            cleaned = cleaned.strip()

            # 提取有用的中文错误信息
            chinese_match = re.search(r'[\u4e00-\u9fff]+', cleaned)
            if chinese_match:
                return chinese_match.group()

            return cleaned

        except Exception:
            return error_msg

    def _log_detailed_error(self, detailed_message):
        """
        记录详细的技术错误信息到详细日志
        不在主界面显示，仅供技术调试使用
        """
        try:
            # 如果有详细日志组件，直接写入
            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                detailed_entry = f"[{current_time}] [DEBUG] {detailed_message}\n"

                try:
                    if self.detailed_log_text.winfo_exists():
                        self.detailed_log_text.insert(tk.END, detailed_entry)
                        self.detailed_log_text.see(tk.END)
                except tk.TclError:
                    pass  # GUI组件已销毁

            # 同时写入到文件日志（如果存在）
            try:
                with open("qmt_ths/trading_system.log", "a", encoding="utf-8") as f:
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{current_time}] [DEBUG] {detailed_message}\n")
            except Exception:
                pass  # 文件写入失败不影响主流程

        except Exception:
            pass  # 详细日志记录失败不影响主流程

    def convert_time(self, unix_timestamp):
        """时间转换函数"""
        traded_time_utc = datetime.fromtimestamp(unix_timestamp, timezone.utc)
        traded_time_beijing = traded_time_utc + timedelta(hours=8)
        return traded_time_beijing.strftime('%Y-%m-%d %H:%M:%S')

    def on_disconnected(self):
        """连接断开"""
        self.logger("交易接口断开，即将重连")


class TongHuaShunTrader:
    def __init__(self):
        self.root = tk.Tk()

        # 初始化窗口管理器
        self.window_manager = WindowManager()

        # 初始化增强日志管理器
        self.enhanced_log_manager = EnhancedLogManager()
        self._log_manager_initialized = True

        # 前台简化日志功能 - 股票信息去重
        self._displayed_stocks = set()  # 记录已显示的股票
        self._last_stock_messages = {}  # 记录每只股票的最后消息

        # 错误抑制机制
        self._error_cache = {}  # 错误消息缓存
        self._error_suppress_time = 60  # 错误抑制时间（秒）

        # 设置窗口位置和大小
        self.window_manager.setup_window(
            self.root,
            "main_window",
            "同花顺 QMT 板块全自动交易"
        )
        
        # 交易相关变量
        self.xt_trader = None
        self.scheduler = None
        self.trading_thread = None
        self.config_file = "trader_config.json"
        
        # 添加状态跟踪变量
        self.last_block_stocks = set()  # 用于存储上次查询的板块股票
        
        # 添加股票交易时间跟踪字典
        self.last_trade_time = {}  # 用于记录每只股票的上次交易时间

        # 添加日志控制变量
        self.last_log_time = datetime.now()  # 上次输出常规日志的时间
        self.log_interval = 1  # 日志输出间隔（秒）- 修改为1秒以便实时查看

        # 添加线程安全的时间缓存
        self._cached_start_time = "09:30:00"
        self._cached_end_time = "14:55:00"
        self._time_cache_lock = threading.Lock()

        # 添加线程安全的配置缓存（完整版本）
        self._cached_config = {
            'block_name': '',
            'account': '',
            'total_amount': '10000',
            'single_amount': '1000',
            'trade_interval': '3',
            'monitor_interval': '1',
            'cancel_interval': '5',
            'reserve_money': '1000',
            'qmt_path': '',
            'ths_path': '',
            'price_type': '0'
        }
        self._config_cache_lock = threading.Lock()
        
        # 添加任务执行状态控制变量
        self.is_monitoring_running = False  # 监控任务是否正在执行
        self.monitor_lock = threading.Lock()  # 添加锁机制，防止并发执行
        self.is_system_stopping = False  # 🔧 新增：系统停止状态标志，防止停止后的回调处理

        # 添加股票过滤设置变量
        self.filter_beijiao = tk.BooleanVar()  # 不买入北交所
        self.filter_chuangye = tk.BooleanVar()  # 不买入创业板
        self.filter_kechuang = tk.BooleanVar()  # 不买入科创板
        self.filter_xinsanban = tk.BooleanVar()  # 不买入新三板
        self.disable_sell = tk.BooleanVar()  # 禁用卖出功能
        self.enable_sold_stock_filter = tk.BooleanVar()  # 启用当日卖出股票过滤功能

        # 添加通用调试控制（可通过界面或配置控制）
        self.enable_detailed_debug = False  # 默认关闭详细调试

        # 添加静默保存防抖控制
        self.last_silent_save_time = 0  # 上次静默保存时间
        self.silent_save_interval = 1.0  # 静默保存间隔（秒），避免频繁保存

        # 添加当日卖出股票跟踪系统
        self.today_sold_stocks = set()  # 当日卖出的股票集合
        self.current_trade_date = datetime.now().date()  # 当前交易日期
        self.daily_trades_file = "daily_trades.json"  # 当日交易记录文件
        self.daily_trades_backup_file = "daily_trades.backup.json"  # 备份文件
        self.trades_lock = threading.Lock()  # 数据访问锁
        self.trades_data_cache = None  # 数据缓存

        try:
            self.create_gui()
            self.load_config()

            # 初始化当日交易跟踪系统
            self._init_daily_trades_system()

            logger.info("程序初始化完成，GUI界面已启动")

        except Exception as e:
            logger.error(f"程序初始化失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise
        
    def create_gui(self):
        # 设置窗体最小尺寸和初始尺寸
        self.root.minsize(1000, 700)
        self.root.geometry("1200x800")

        # 配置根窗口的grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置主框架的grid权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)  # 日志区域可扩展
        
        # 第一行参数区域 - 独立的Frame
        path_frame = ttk.LabelFrame(main_frame, text="路径设置", padding="3")
        path_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=3, pady=3)
        
        # 证券账号
        ttk.Label(path_frame, text="证券账号:").grid(row=0, column=0, sticky=tk.W)
        self.account_entry = ttk.Entry(path_frame, width=10)
        self.account_entry.grid(row=0, column=1, sticky=tk.W, padx=(0,15))
        self.account_entry.bind('<FocusOut>', lambda e: self.on_focus_out_save())
        
        # 同花顺板块目录
        ttk.Label(path_frame, text="同花顺板块目录:").grid(row=0, column=2, sticky=tk.W, padx=(3,0))
        self.ths_path_entry = ttk.Entry(path_frame, width=35)
        self.ths_path_entry.grid(row=0, column=3, sticky=tk.W)
        self.ths_path_entry.insert(0, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686")
        self.ths_path_entry.bind('<FocusIn>', lambda e: self.on_entry_click(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686"))
        self.ths_path_entry.bind('<FocusOut>', lambda e: self.on_focus_out(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686"))
        
        # QMT报单目录
        ttk.Label(path_frame, text="QMT报单目录:").grid(row=0, column=4, sticky=tk.W, padx=(3,0))
        self.qmt_path_entry = ttk.Entry(path_frame, width=35)
        self.qmt_path_entry.grid(row=0, column=5, sticky=tk.W)
        self.qmt_path_entry.insert(0, "示例：D:\\国金QMT交易端模拟\\userdata_mini")
        self.qmt_path_entry.bind('<FocusIn>', lambda e: self.on_entry_click(self.qmt_path_entry, "示例：D:\\国金QMT交易端模拟\\userdata_mini"))
        self.qmt_path_entry.bind('<FocusOut>', lambda e: self.on_focus_out(self.qmt_path_entry, "示例：D:\\国金QMT交易端模拟\\userdata_mini"))
        
        # 交易参数区域 - 独立的Frame
        trade_frame = ttk.LabelFrame(main_frame, text="交易参数", padding="3")
        trade_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=3, pady=3)
        
        # 板块设置
        ttk.Label(trade_frame, text="板块名称:").grid(row=0, column=0, sticky=tk.W)
        self.block_name_entry = ttk.Entry(trade_frame, width=15)
        self.block_name_entry.grid(row=0, column=1, sticky=tk.W, padx=(0,5))
        self.block_name_entry.bind('<FocusOut>', lambda e: self.on_focus_out_save())
        
        # 添加刷新板块按钮
        self.refresh_block_button = ttk.Button(trade_frame, text="刷新板块", command=self.refresh_block_info)
        self.refresh_block_button.grid(row=0, column=2, padx=5)
        
        # 保留资金
        ttk.Label(trade_frame, text="保留资金:").grid(row=0, column=3, sticky=tk.W, padx=(10,0))
        self.reserve_money_entry = ttk.Entry(trade_frame, width=12)
        self.reserve_money_entry.grid(row=0, column=4, sticky=tk.W, padx=(0,15))
        self.reserve_money_entry.insert(0, "10000")
        
        # 交易时间设置
        ttk.Label(trade_frame, text="开始时间:").grid(row=1, column=0, sticky=tk.W)
        self.start_time = ttk.Entry(trade_frame, width=10)
        self.start_time.grid(row=1, column=1, sticky=tk.W)
        self.start_time.insert(0, "09:30:00")
        
        ttk.Label(trade_frame, text="结束时间:").grid(row=1, column=2, sticky=tk.W)
        self.end_time = ttk.Entry(trade_frame, width=10)
        self.end_time.grid(row=1, column=3, sticky=tk.W)
        self.end_time.insert(0, "14:55:00")
        
        # 委托设置
        ttk.Label(trade_frame, text="委托类型:").grid(row=2, column=0, sticky=tk.W)
        self.price_type = ttk.Combobox(trade_frame, width=15, 
            values=["限价", "最优五档", "最优五档转限价", "对手方最优", "本方最优"])
        self.price_type.grid(row=2, column=1, sticky=tk.W)
        self.price_type.set("对手方最优")
        
        ttk.Label(trade_frame, text="价格调整(%):").grid(row=2, column=2, sticky=tk.W)
        self.price_adjust = ttk.Entry(trade_frame, width=10)
        self.price_adjust.grid(row=2, column=3, sticky=tk.W)
        self.price_adjust.insert(0, "0.5")
        
        # 金额设置
        ttk.Label(trade_frame, text="单笔金额:").grid(row=3, column=0, sticky=tk.W)
        self.single_amount_entry = ttk.Entry(trade_frame, width=12)
        self.single_amount_entry.grid(row=3, column=1, sticky=tk.W)
        self.single_amount_entry.insert(0, "5000")
        
        ttk.Label(trade_frame, text="交易间隔(秒):").grid(row=3, column=2, sticky=tk.W)
        self.trade_interval_entry = ttk.Entry(trade_frame, width=8)
        self.trade_interval_entry.grid(row=3, column=3, sticky=tk.W)
        self.trade_interval_entry.insert(0, "1.0")
        
        ttk.Label(trade_frame, text="单只总金额:").grid(row=4, column=0, sticky=tk.W)
        self.total_amount_entry = ttk.Entry(trade_frame, width=12)
        self.total_amount_entry.grid(row=4, column=1, sticky=tk.W)
        self.total_amount_entry.insert(0, "20000")

        # 🔧 修复：删除重复的保留资金输入框，避免UI混淆
        # 保留资金输入框已在第0行创建，此处不再重复

        # 监控间隔设置
        ttk.Label(trade_frame, text="板块监控间隔:").grid(row=5, column=0, sticky=tk.W)
        monitor_frame = ttk.Frame(trade_frame)
        monitor_frame.grid(row=5, column=1, sticky=tk.W)

        self.monitor_interval_entry = ttk.Entry(monitor_frame, width=6)
        self.monitor_interval_entry.pack(side=tk.LEFT)
        self.monitor_interval_entry.insert(0, "1")

        self.monitor_unit_combo = ttk.Combobox(monitor_frame, width=4, values=["秒", "分钟"], state="readonly")
        self.monitor_unit_combo.pack(side=tk.LEFT, padx=(2,0))
        self.monitor_unit_combo.set("秒")

        ttk.Label(trade_frame, text="撤单任务间隔:").grid(row=5, column=2, sticky=tk.W)
        cancel_frame = ttk.Frame(trade_frame)
        cancel_frame.grid(row=5, column=3, sticky=tk.W)

        self.cancel_interval_entry = ttk.Entry(cancel_frame, width=6)
        self.cancel_interval_entry.pack(side=tk.LEFT)
        self.cancel_interval_entry.insert(0, "10")

        self.cancel_unit_combo = ttk.Combobox(cancel_frame, width=4, values=["秒", "分钟"], state="readonly")
        self.cancel_unit_combo.pack(side=tk.LEFT, padx=(2,0))
        self.cancel_unit_combo.set("秒")

        # 设置监控参数验证
        self.setup_monitor_validation()

        # 股票过滤设置区域
        filter_frame = ttk.LabelFrame(main_frame, text="股票过滤设置", padding="3")
        filter_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=3, pady=3)

        # 不买入股票类型勾选框
        ttk.Label(filter_frame, text="不买入股票类型:").grid(row=0, column=0, sticky=tk.W)

        self.beijiao_check = ttk.Checkbutton(filter_frame, text="北交所", variable=self.filter_beijiao)
        self.beijiao_check.grid(row=0, column=1, sticky=tk.W, padx=5)

        self.chuangye_check = ttk.Checkbutton(filter_frame, text="创业板", variable=self.filter_chuangye)
        self.chuangye_check.grid(row=0, column=2, sticky=tk.W, padx=5)

        self.kechuang_check = ttk.Checkbutton(filter_frame, text="科创板", variable=self.filter_kechuang)
        self.kechuang_check.grid(row=0, column=3, sticky=tk.W, padx=5)

        self.xinsanban_check = ttk.Checkbutton(filter_frame, text="新三板", variable=self.filter_xinsanban)
        self.xinsanban_check.grid(row=0, column=4, sticky=tk.W, padx=5)

        # 禁用卖出功能设置
        ttk.Label(filter_frame, text="卖出控制:").grid(row=1, column=0, sticky=tk.W)
        self.disable_sell_check = ttk.Checkbutton(filter_frame, text="禁用卖出功能", variable=self.disable_sell)
        self.disable_sell_check.grid(row=1, column=1, sticky=tk.W, padx=5)

        # 按钮区域
        button_frame = ttk.Frame(main_frame, padding="3")
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=3, pady=3)
        
        self.save_button = ttk.Button(button_frame, text="保存参数", command=self.save_config)
        self.save_button.grid(row=0, column=0, padx=5)
        
        self.clear_log_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.grid(row=0, column=1, padx=5)
        
        self.start_button = ttk.Button(button_frame, text="开始交易", command=self.start_trading)
        self.start_button.grid(row=0, column=2, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止交易", command=self.stop_trading)
        self.stop_button.grid(row=0, column=3, padx=5)
        self.stop_button.state(['disabled'])
        
        # 添加状态显示
        ttk.Label(button_frame, text="状态:").grid(row=0, column=4, padx=(15,0), sticky=tk.W)
        self.status_label = ttk.Label(button_frame, text="未启动", foreground="gray")
        self.status_label.grid(row=0, column=5, padx=5, sticky=tk.W)
        
        # 日志区域 - 使用Notebook分页显示
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="3")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=3, pady=3)

        # 配置日志框架的grid权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 创建Notebook用于分页显示
        self.log_notebook = ttk.Notebook(log_frame)
        self.log_notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 简化日志页面（前台显示）
        simplified_frame = ttk.Frame(self.log_notebook)
        self.log_notebook.add(simplified_frame, text="交易日志")

        # 配置简化日志框架的grid权重
        simplified_frame.columnconfigure(0, weight=1)
        simplified_frame.rowconfigure(0, weight=1)

        self.simplified_log_text = tk.Text(simplified_frame, height=20, width=125, wrap=tk.WORD)
        self.simplified_log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        simplified_scrollbar = ttk.Scrollbar(simplified_frame, orient=tk.VERTICAL, command=self.simplified_log_text.yview)
        simplified_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.simplified_log_text['yscrollcommand'] = simplified_scrollbar.set

        # 详细日志页面（调试功能）
        detailed_frame = ttk.Frame(self.log_notebook)
        self.log_notebook.add(detailed_frame, text="详细日志")

        # 配置详细日志框架的grid权重
        detailed_frame.columnconfigure(0, weight=1)
        detailed_frame.rowconfigure(0, weight=1)

        self.detailed_log_text = tk.Text(detailed_frame, height=20, width=125, wrap=tk.WORD)
        self.detailed_log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        detailed_scrollbar = ttk.Scrollbar(detailed_frame, orient=tk.VERTICAL, command=self.detailed_log_text.yview)
        detailed_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.detailed_log_text['yscrollcommand'] = detailed_scrollbar.set

        # 保持向后兼容性
        self.log_text = self.simplified_log_text

        # 日志管理按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(log_button_frame, text="清空交易日志", command=self.clear_simplified_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="清空详细日志", command=self.clear_detailed_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="重置股票显示", command=self.reset_stock_display).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="导出日志", command=self.export_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="日志统计", command=self.show_log_stats).pack(side=tk.LEFT)

        # GUI创建完成后，设置日志回调函数
        self._setup_log_callbacks()

    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 设置各个输入框的值，并设置适当的文本颜色
                self.set_entry_value(self.account_entry, config.get('account', ''))
                self.set_entry_value(self.ths_path_entry, config.get('ths_path', ''), "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686")
                self.set_entry_value(self.qmt_path_entry, config.get('qmt_path', ''), "示例：D:\\国金QMT交易端模拟\\userdata_mini")
                self.set_entry_value(self.block_name_entry, config.get('block_name', ''), "输入板块名称")
                
                # 清空并设置时间
                self.start_time.delete(0, tk.END)
                self.start_time.insert(0, config.get('start_time', '09:30:00'))
                
                self.end_time.delete(0, tk.END)
                self.end_time.insert(0, config.get('end_time', '14:55:00'))
                
                # 清空并设置金额相关的输入框
                self.single_amount_entry.delete(0, tk.END)
                self.single_amount_entry.insert(0, config.get('single_amount', '5000'))
                
                self.total_amount_entry.delete(0, tk.END)
                self.total_amount_entry.insert(0, config.get('total_amount', '20000'))
                
                # 设置价格调整幅度
                self.price_adjust.delete(0, tk.END)
                self.price_adjust.insert(0, config.get('price_adjust', '0.5'))
                
                # 设置委托类型
                self.price_type.set(config.get('price_type', '对手方最优'))
                
                self.reserve_money_entry.delete(0, tk.END)
                self.reserve_money_entry.insert(0, config.get('reserve_money', '10000'))
                
                self.trade_interval_entry.delete(0, tk.END)
                self.trade_interval_entry.insert(0, config.get('trade_interval', '1'))

                # 加载股票过滤设置
                self.filter_beijiao.set(config.get('filter_beijiao', False))
                self.filter_chuangye.set(config.get('filter_chuangye', False))
                self.filter_kechuang.set(config.get('filter_kechuang', False))
                self.filter_xinsanban.set(config.get('filter_xinsanban', False))
                self.disable_sell.set(config.get('disable_sell', False))
                self.enable_sold_stock_filter.set(config.get('enable_filter', True))

                # 加载监控参数设置
                self.monitor_interval_entry.delete(0, tk.END)
                self.monitor_interval_entry.insert(0, config.get('monitor_interval', '1'))

                self.monitor_unit_combo.set(config.get('monitor_unit', '秒'))

                self.cancel_interval_entry.delete(0, tk.END)
                self.cancel_interval_entry.insert(0, config.get('cancel_interval', '10'))

                self.cancel_unit_combo.set(config.get('cancel_unit', '秒'))

                # 加载窗口设置
                self.load_window_settings(config.get('window_settings', {}))

                self.log_message("配置加载成功")
            else:
                # 如果配置文件不存在，设置默认值
                self.start_time.insert(0, '09:30:00')
                self.end_time.insert(0, '14:55:00')
                
                self.single_amount_entry.insert(0, '5000')
                self.total_amount_entry.insert(0, '20000')
                
                self.price_adjust.insert(0, '0.5')
                
        except Exception as e:
            self.log_message(f"加载配置文件失败: {str(e)}")

    def load_window_settings(self, window_settings):
        """加载窗口设置"""
        try:
            if not window_settings:
                return

            # 更新窗口管理器的配置
            self.window_manager.update_window_config(
                "main_window",
                width=window_settings.get("width", 920),
                height=window_settings.get("height", 600),
                x=window_settings.get("x"),
                y=window_settings.get("y"),
                remember_position=window_settings.get("remember_position", True),
                center_on_startup=window_settings.get("center_on_startup", False)
            )

            # 如果窗口已经创建，应用设置
            if hasattr(self, 'root') and self.root:
                width = window_settings.get("width", 920)
                height = window_settings.get("height", 600)
                x = window_settings.get("x")
                y = window_settings.get("y")

                if x is not None and y is not None:
                    self.root.geometry(f"{width}x{height}+{x}+{y}")
                else:
                    self.root.geometry(f"{width}x{height}")

        except Exception as e:
            self.log_message(f"加载窗口设置失败: {str(e)}")

    def setup_monitor_validation(self):
        """设置监控参数验证"""
        def validate_monitor_interval(event=None):
            try:
                value = float(self.monitor_interval_entry.get())
                unit = self.monitor_unit_combo.get()

                if unit == "秒":
                    if not (1 <= value <= 3600):
                        self.monitor_interval_entry.config(foreground='red')
                        return False
                else:  # 分钟
                    if not (1/60 <= value <= 60):
                        self.monitor_interval_entry.config(foreground='red')
                        return False

                self.monitor_interval_entry.config(foreground='black')
                return True
            except ValueError:
                self.monitor_interval_entry.config(foreground='red')
                return False

        def validate_cancel_interval(event=None):
            try:
                value = float(self.cancel_interval_entry.get())
                unit = self.cancel_unit_combo.get()

                if unit == "秒":
                    if not (5 <= value <= 1800):
                        self.cancel_interval_entry.config(foreground='red')
                        return False
                else:  # 分钟
                    if not (5/60 <= value <= 30):
                        self.cancel_interval_entry.config(foreground='red')
                        return False

                self.cancel_interval_entry.config(foreground='black')
                return True
            except ValueError:
                self.cancel_interval_entry.config(foreground='red')
                return False

        # 绑定验证事件
        self.monitor_interval_entry.bind('<KeyRelease>', validate_monitor_interval)
        self.monitor_unit_combo.bind('<<ComboboxSelected>>', validate_monitor_interval)
        self.cancel_interval_entry.bind('<KeyRelease>', validate_cancel_interval)
        self.cancel_unit_combo.bind('<<ComboboxSelected>>', validate_cancel_interval)

    def save_config_silent(self):
        """静默保存当前配置到文件（无弹窗提示）"""
        # 先加载现有配置（包括价格限制等不在此界面显示的配置）
        existing_config = {}
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
        except Exception:
            pass  # 如果加载失败，使用空配置

        config = {
            'account': self.get_entry_value(self.account_entry, ""),
            'ths_path': self.get_entry_value(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686"),
            'qmt_path': self.get_entry_value(self.qmt_path_entry, "示例：D:\\国金QMT交易端模拟\\userdata_mini"),
            'block_name': self.get_entry_value(self.block_name_entry, "输入板块名称"),
            'start_time': self.start_time.get(),
            'end_time': self.end_time.get(),
            'single_amount': self.single_amount_entry.get(),
            'total_amount': self.total_amount_entry.get(),
            'price_type': self.price_type.get(),
            'price_adjust': self.price_adjust.get(),
            'reserve_money': self.reserve_money_entry.get(),
            'trade_interval': self.trade_interval_entry.get(),
            'enable_filter': self.enable_sold_stock_filter.get(),
            'filter_beijiao': self.filter_beijiao.get(),
            'filter_chuangye': self.filter_chuangye.get(),
            'filter_kechuang': self.filter_kechuang.get(),
            'filter_xinsanban': self.filter_xinsanban.get(),
            # 监控参数设置
            'monitor_interval': self.monitor_interval_entry.get(),
            'monitor_unit': self.monitor_unit_combo.get(),
            'cancel_interval': self.cancel_interval_entry.get(),
            'cancel_unit': self.cancel_unit_combo.get(),
            # 窗口设置
            'window_settings': self.get_current_window_settings()
        }
        
        # 保留现有配置中的价格限制设置
        if 'price_limits' in existing_config:
            config['price_limits'] = existing_config['price_limits']
        else:
            # 如果没有现有价格限制设置，使用默认值
            config['price_limits'] = {
                'default': 3000,
                'chuangye': 500,
                'kechuang': 1000,
                'zhuban': 1000,
                'beijiao': 200,
                'st': 50,
                'index': 5000,
                'hk': 1000,
                'us': 10000,
                'futures': 100000
            }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            # 静默保存：不显示成功消息，不记录日志（避免日志刷屏）
        except Exception as e:
            # 静默保存：只在失败时记录日志，不显示弹窗
            self.log_message(f"静默保存配置失败: {str(e)}")
            
    def save_config(self):
        """保存当前配置到文件"""
        # 先加载现有配置（包括价格限制等不在此界面显示的配置）
        existing_config = {}
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)
        except Exception:
            pass  # 如果加载失败，使用空配置

        config = {
            'account': self.account_entry.get(),
            'ths_path': self.ths_path_entry.get(),
            'qmt_path': self.qmt_path_entry.get(),
            'block_name': self.block_name_entry.get(),
            'start_time': self.start_time.get(),
            'end_time': self.end_time.get(),
            'single_amount': self.single_amount_entry.get(),
            'total_amount': self.total_amount_entry.get(),
            'price_type': self.price_type.get(),
            'price_adjust': self.price_adjust.get(),
            'reserve_money': self.reserve_money_entry.get(),
            'trade_interval': self.trade_interval_entry.get(),
            'filter_beijiao': self.filter_beijiao.get(),
            'filter_chuangye': self.filter_chuangye.get(),
            'filter_kechuang': self.filter_kechuang.get(),
            'filter_xinsanban': self.filter_xinsanban.get(),
            'disable_sell': self.disable_sell.get(),
            # 监控参数设置
            'monitor_interval': self.monitor_interval_entry.get(),
            'monitor_unit': self.monitor_unit_combo.get(),
            'cancel_interval': self.cancel_interval_entry.get(),
            'cancel_unit': self.cancel_unit_combo.get(),
            # 窗口设置
            'window_settings': self.get_current_window_settings()
        }
        
        # 保留现有配置中的价格限制设置
        if 'price_limits' in existing_config:
            config['price_limits'] = existing_config['price_limits']
        else:
            # 如果没有现有价格限制设置，使用默认值
            config['price_limits'] = {
                'default': 3000,
                'chuangye': 500,
                'kechuang': 1000,
                'zhuban': 1000,
                'beijiao': 200,
                'st': 50,
                'index': 5000,
                'hk': 1000,
                'us': 10000,
                'futures': 100000
            }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            # 保存成功后，重启调度器以应用新配置
            self.restart_scheduler_if_running()

            messagebox.showinfo("成功", "配置保存成功，监控间隔已更新")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def get_current_window_settings(self):
        """获取当前窗口设置"""
        try:
            # 获取当前窗口几何信息
            geometry = self.root.geometry()

            # 解析geometry字符串 "920x600+100+50"
            if '+' in geometry:
                size_part, pos_part = geometry.split('+', 1)
                if '+' in pos_part:
                    x_str, y_str = pos_part.split('+', 1)
                else:
                    # 处理负坐标的情况
                    parts = pos_part.split('-')
                    if len(parts) == 2:
                        x_str = '-' + parts[1]
                        y_str = '0'
                    else:
                        x_str, y_str = '0', '0'
            else:
                size_part = geometry
                x_str, y_str = '0', '0'

            # 解析尺寸
            if 'x' in size_part:
                width_str, height_str = size_part.split('x')
            else:
                width_str, height_str = '920', '600'

            return {
                "width": int(width_str),
                "height": int(height_str),
                "x": int(x_str),
                "y": int(y_str),
                "remember_position": True,
                "center_on_startup": False
            }
        except Exception as e:
            self.log_message(f"获取窗口设置失败: {str(e)}")
            return {
                "width": 920,
                "height": 600,
                "x": 100,
                "y": 50,
                "remember_position": True,
                "center_on_startup": True
            }

    def get_interval_in_seconds(self, interval_str, unit):
        """将间隔时间转换为秒数"""
        try:
            interval = float(interval_str)
            if unit == "分钟":
                return int(interval * 60)
            else:  # 秒
                return int(interval)
        except ValueError:
            return 1 if unit == "秒" else 60  # 默认值

    def get_monitor_intervals_from_config(self):
        """从配置获取监控间隔"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                monitor_seconds = self.get_interval_in_seconds(
                    config.get('monitor_interval', '1'),
                    config.get('monitor_unit', '秒')
                )

                cancel_seconds = self.get_interval_in_seconds(
                    config.get('cancel_interval', '10'),
                    config.get('cancel_unit', '秒')
                )

                return monitor_seconds, cancel_seconds
            else:
                return 1, 10  # 默认值
        except Exception as e:
            self.log_message(f"读取监控间隔配置失败: {str(e)}")
            return 1, 10

    def restart_scheduler_if_running(self):
        """如果调度器正在运行，则重启以应用新配置"""
        if hasattr(self, 'scheduler') and self.scheduler and self.scheduler.running:
            self.log_message("检测到监控间隔配置变更，重启调度器...")

            # 保存当前交易状态
            was_trading = hasattr(self, 'trading') and self.trading

            # 重新初始化调度器
            self.init_scheduler()

            # 如果之前在交易，继续交易状态
            if was_trading:
                self.trading = True
                self.log_message("调度器重启完成，交易状态已恢复")
            else:
                self.log_message("调度器重启完成")

    def validate_trade_interval(self):
        """验证交易间隔是否为有效的浮点数"""
        try:
            interval = float(self.trade_interval_entry.get())
            if interval <= 0:
                raise ValueError("交易间隔必须大于0")
            return True
        except ValueError as e:
            messagebox.showerror("错误", f"无效的交易间隔: {str(e)}")
            return False

    def _init_daily_trades_system(self):
        """初始化当日交易跟踪系统"""
        try:
            logger.info("🔄 初始化当日交易跟踪系统...")

            # 检查并创建必要的目录（如果文件在子目录中）
            file_dir = os.path.dirname(self.daily_trades_file)
            if file_dir and not os.path.exists(file_dir):
                os.makedirs(file_dir, exist_ok=True)
                logger.info(f"📁 创建目录: {file_dir}")

            # 加载当日交易数据
            logger.info("📂 开始加载当日交易数据...")
            self._load_daily_trades()
            logger.info("📂 当日交易数据加载完成")

            # 检查日期变化
            logger.info("📅 开始检查日期变化...")
            self._check_trade_date_change()

            logger.info("📅 日期变化检查完成")

            success_msg = f"当日交易跟踪系统初始化完成，已跟踪 {len(self.today_sold_stocks)} 只卖出股票"
            logger.info(success_msg)

            # 延迟添加到GUI日志，确保GUI完全初始化
            self.root.after(100, lambda: self._safe_log_message("🔄 当日交易跟踪系统已启动"))
            self.root.after(200, lambda: self._safe_log_message(success_msg))

        except Exception as e:
            error_msg = f"❌ 初始化当日交易跟踪系统失败: {str(e)}"
            logger.error(error_msg)
            logger.error(f"错误详情: {str(e)}")
            logger.error(traceback.format_exc())

            # 初始化失败时使用默认值，确保程序能正常运行
            self.today_sold_stocks = set()
            self.current_trade_date = datetime.now().date()
            logger.warning("🔄 使用默认值继续运行")

            # 延迟添加错误日志到GUI
            self.root.after(100, lambda: self._safe_log_message(error_msg))

    def _load_daily_trades(self):
        """加载当日交易记录"""
        try:
            with self.trades_lock:
                if os.path.exists(self.daily_trades_file):
                    with open(self.daily_trades_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 验证数据格式和完整性
                    if self._validate_daily_trades_data(data):
                        file_date = datetime.strptime(data['trade_date'], '%Y-%m-%d').date()
                        current_date = datetime.now().date()

                        # 只加载当日数据
                        if file_date == current_date:
                            self.today_sold_stocks = set(data.get('sold_stocks', []))
                            self.current_trade_date = file_date
                            self.trades_data_cache = data
                            logger.info(f"📂 成功加载当日交易记录: {len(self.today_sold_stocks)} 只股票")
                        else:
                            # 非当日数据，清空并创建新记录
                            logger.warning(f"📅 检测到日期变化 {file_date} -> {current_date}，清空历史记录")
                            self._create_new_daily_trades()
                    else:
                        logger.warning("⚠️ 交易记录文件格式异常，尝试从备份恢复")
                        try:
                            if self._restore_from_backup():
                                success_msg = f"从备份恢复当日交易记录: {len(self.today_sold_stocks)} 只股票"
                                logger.info(success_msg)
                                self.log_message(success_msg)
                            else:
                                # 备份文件无效，创建新的交易记录
                                logger.warning("备份文件无效，创建新的交易记录")
                                self._create_new_trades_record()
                        except Exception as restore_error:
                            logger.error(f"从备份恢复失败: {str(restore_error)}")
                            logger.error(traceback.format_exc())
                            # 创建新的交易记录
                            self._create_new_trades_record()
                else:
                    # 文件不存在，创建新记录
                    logger.info("📝 交易记录文件不存在，创建新记录")
                    self._create_new_daily_trades()

        except Exception as e:
            logger.error(f"❌ 加载当日交易记录失败: {str(e)}")
            self._restore_from_backup()

    def _save_daily_trades(self):
        """保存当日交易记录"""
        try:
            # 创建备份
            logger.info("🔄 开始创建备份...")
            self._backup_daily_trades()
            logger.info("🔄 备份创建完成")

            # 准备数据
            logger.info("📝 开始准备数据...")
            data = {
                "version": "1.0",
                "trade_date": self.current_trade_date.isoformat(),
                "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "sold_stocks": list(self.today_sold_stocks),
                "metadata": {
                    "total_sold_count": len(self.today_sold_stocks),
                    "enable_filter": self.enable_sold_stock_filter.get() if hasattr(self.enable_sold_stock_filter, 'get') else bool(self.enable_sold_stock_filter)
                }
            }

            # 原子写入：先写临时文件，再重命名
            logger.info("💾 开始写入文件...")
            temp_file = self.daily_trades_file + ".tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 原子重命名
            logger.info("🔄 开始重命名文件...")
            os.replace(temp_file, self.daily_trades_file)

            # 更新缓存
            self.trades_data_cache = data

            logger.info(f"💾 当日交易记录已保存: {len(self.today_sold_stocks)} 只股票")

        except Exception as e:
            logger.error(f"❌ 保存当日交易记录失败: {str(e)}")

    def _backup_daily_trades(self):
        """备份当日交易记录"""
        try:
            if os.path.exists(self.daily_trades_file):
                # 创建备份文件
                import shutil
                shutil.copy2(self.daily_trades_file, self.daily_trades_backup_file)

                # 创建带时间戳的备份
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                timestamped_backup = f"daily_trades_{timestamp}.json"
                shutil.copy2(self.daily_trades_file, timestamped_backup)

                # 清理旧备份（保留最近3个）
                self._cleanup_old_backups()

                logger.info(f"✅ 创建备份文件成功: {self.daily_trades_backup_file}")
                logger.info(f"✅ 创建带时间戳的备份成功: {timestamped_backup}")

        except Exception as e:
            logger.warning(f"⚠️ 备份当日交易记录失败: {str(e)}")

    def _validate_daily_trades_data(self, data):
        """验证当日交易数据的完整性"""
        try:
            # 检查必要字段
            required_fields = ['version', 'trade_date', 'sold_stocks']
            for field in required_fields:
                if field not in data:
                    msg = f"交易记录缺少必要字段: {field}"
                    logger.warning(msg)
                    self.log_message(msg)
                    return False

            # 检查数据类型
            if not isinstance(data['sold_stocks'], list):
                msg = "sold_stocks字段类型错误"
                logger.warning(msg)
                self.log_message(msg)
                return False

            # 检查日期格式
            try:
                datetime.strptime(data['trade_date'], '%Y-%m-%d')
            except ValueError:
                msg = "交易日期格式错误"
                logger.warning(msg)
                self.log_message(msg)
                return False

            logger.info("✅ 交易数据验证通过")
            return True

        except Exception as e:
            logger.error(f"验证交易数据失败: {str(e)}")
            return False

    def _restore_from_backup(self):
        """从备份文件恢复数据"""
        try:
            if os.path.exists(self.daily_trades_backup_file):
                logger.info(f"🔄 开始从备份恢复数据: {self.daily_trades_backup_file}")
                with open(self.daily_trades_backup_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if self._validate_daily_trades_data(data):
                    # 恢复数据
                    file_date = datetime.strptime(data['trade_date'], '%Y-%m-%d').date()
                    current_date = datetime.now().date()

                    if file_date == current_date:
                        self.today_sold_stocks = set(data.get('sold_stocks', []))
                        self.current_trade_date = file_date
                        msg = f"✅ 从备份恢复当日交易记录: {len(self.today_sold_stocks)} 只股票"
                        logger.info(msg)
                        self.log_message(msg)

                        # 重新保存主文件
                        self._save_daily_trades()
                        return

            # 备份恢复失败，创建新记录
            msg = "⚠️ 备份文件无效，创建新的交易记录"
            logger.warning(msg)
            self.log_message(msg)
            self._create_new_daily_trades()

        except Exception as e:
            logger.error(f"从备份恢复失败: {str(e)}")
            self.log_message(f"从备份恢复失败: {str(e)}")
            self._create_new_daily_trades()

    def _create_new_daily_trades(self):
        """创建新的当日交易记录"""
        try:
            logger.info("🔧 开始创建新的当日交易记录...")
            self.today_sold_stocks = set()
            self.current_trade_date = datetime.now().date()
            logger.info("💾 开始保存交易记录...")
            self._save_daily_trades()
            logger.info("📝 已创建新的当日交易记录")

        except Exception as e:
            logger.error(f"❌ 创建新交易记录失败: {str(e)}")
            logger.error(traceback.format_exc())

    def _check_trade_date_change(self):
        """检查交易日期变化"""
        try:
            current_date = datetime.now().date()
            if current_date != self.current_trade_date:
                logger.info(f"📅 检测到交易日期变化: {self.current_trade_date} -> {current_date}")

                # 清空当日卖出记录
                self.today_sold_stocks.clear()
                self.current_trade_date = current_date

                # 保存新的记录
                self._save_daily_trades()

                logger.info("🔄 已清空前一交易日的卖出记录")

        except Exception as e:
            logger.error(f"❌ 检查交易日期变化失败: {str(e)}")
            logger.error(traceback.format_exc())

    def _cleanup_old_backups(self):
        """清理旧的备份文件，只保留最近的3个"""
        try:
            import glob
            # 获取所有带时间戳的备份文件
            backup_files = glob.glob("daily_trades_*.json")
            # 按修改时间排序
            backup_files.sort(key=os.path.getmtime)
            
            # 删除多余的备份文件（保留最近3个）
            if len(backup_files) > 3:
                old_backups = backup_files[:-3]
                for old_backup in old_backups:
                    try:
                        os.remove(old_backup)
                        logger.info(f"🗑️ 已清理旧备份文件: {old_backup}")
                    except Exception as e:
                        logger.warning(f"⚠️ 清理备份文件失败 {old_backup}: {str(e)}")
                        
        except Exception as e:
            logger.warning(f"⚠️ 清理旧备份失败: {str(e)}")

    def is_stock_filtered(self, stock_code):
        """检查股票是否应该被过滤（不买入）"""
        try:
            # 北交所股票过滤
            if self.filter_beijiao.get():
                # 北交所股票：8开头、4开头、9开头的6位数字，或带.BJ后缀
                if (stock_code.isdigit() and len(stock_code) == 6 and stock_code.startswith(('8', '4', '9'))) or stock_code.endswith('.BJ'):
                    return True

            # 创业板股票过滤
            if self.filter_chuangye.get():
                # 创业板股票：300开头或带.SZ后缀且以300开头
                if stock_code.startswith('300') or (stock_code.endswith('.SZ') and stock_code.startswith('300')):
                    return True

            # 科创板股票过滤
            if self.filter_kechuang.get():
                # 科创板股票：688开头或带.SH后缀且以688开头
                if stock_code.startswith('688') or (stock_code.endswith('.SH') and stock_code.startswith('688')):
                    return True

            # 新三板股票过滤
            if self.filter_xinsanban.get():
                # 新三板股票：带.NQ后缀
                if stock_code.endswith('.NQ'):
                    return True

            return False
        except Exception as e:
            self.log_message(f"股票过滤检查失败 {stock_code}: {str(e)}")
            return False



    def _safe_log_message(self, message, level="INFO"):
        """安全的日志记录方法 - 增强版（含错误抑制）"""
        try:
            # 检查是否应该抑制重复错误
            if self._should_suppress_error(message):
                return  # 抑制重复错误，不显示

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            detailed_message = f"[{current_time}] [{level}] {message}"

            # 检查GUI组件是否可用
            gui_available = (
                hasattr(self, 'root') and self.root and
                hasattr(self, 'simplified_log_text') and
                hasattr(self, 'detailed_log_text')
            )

            if not gui_available:
                print(f"[{level}] {message} (GUI不可用)")
                return

            # 更新详细日志（显示所有日志）
            try:
                if self.detailed_log_text and self.detailed_log_text.winfo_exists():
                    self.detailed_log_text.insert(tk.END, f"{detailed_message}\n")
                    self.detailed_log_text.see(tk.END)
            except tk.TclError:
                pass  # GUI组件已销毁

            # 更新简化日志（过滤调试信息）
            try:
                if self.simplified_log_text and self.simplified_log_text.winfo_exists():
                    if not self._should_filter_from_simplified_log(message):
                        simple_time = datetime.now().strftime("%H:%M:%S")
                        simple_message = f"[{simple_time}] {message}"
                        self.simplified_log_text.insert(tk.END, f"{simple_message}\n")
                        self.simplified_log_text.see(tk.END)
            except tk.TclError:
                pass  # GUI组件已销毁

            # 移除log_text的重复写入
            # 因为log_text = simplified_log_text，会导致重复显示
            # 现在简化日志和详细日志已经分别处理，不需要额外的log_text写入

        except Exception as e:
            # 如果GUI更新失败，至少打印到控制台
            try:
                print(f"[{level}] {message}")
                print(f"GUI日志记录失败: {e}")
            except:
                pass  # 避免无限递归

    def _should_filter_from_simplified_log(self, message):
        """判断是否应该从简化日志中过滤掉此消息 - 增强版"""
        import re

        # 检查是否是股票交易信息（重要信息，需要显示）
        stock_pattern = r'\d{6}\([^)]+\):'
        if re.search(stock_pattern, message):
            # 提取股票代码
            stock_match = re.search(r'(\d{6})\([^)]+\):', message)
            if stock_match:
                stock_code = stock_match.group(1)
                # 检查是否是新的股票信息或状态变化
                if stock_code not in self._last_stock_messages or self._last_stock_messages[stock_code] != message:
                    self._last_stock_messages[stock_code] = message
                    return False  # 显示新的或变化的股票信息
                else:
                    return True   # 过滤重复的股票信息

        # 过滤掉过于详细的调试信息和操作细节
        filter_patterns = [
            # 行情获取调试信息
            "获取.*行情数据",
            "详细行情获取调试",
            "准备尝试的代码格式",
            "尝试获取代码",
            "获取到行情数据",

            # 价格计算调试信息
            "买入价格计算:",
            "卖出价格计算:",
            "价格类型:",
            "价格调整:",

            # 五档行情信息
            "📊.*五档行情:",
            "卖五:",
            "卖四:",
            "卖三:",
            "卖二:",
            "卖一:",
            "最新:",
            "买一:",
            "买二:",
            "买三:",
            "买四:",
            "买五:",
            "五档无合适价格",
            "转限价委托:",

            # 涨跌停价格信息
            "📊.*从接口获取涨跌停价格:",
            "涨停价=",
            "跌停价=",
            "最新价:",
            "涨停价:",
            "跌停价:",
            "买入价格.*超过涨停价",
            "卖出价格.*低于跌停价",
            "调整为涨停价",
            "调整为跌停价",
            "最终委托价格:",

            # 监控任务信息
            "监控任务执行",
            "上一个监控任务仍在执行中",
            "定时任务执行完成",
            "监控板块.*中的.*只股票",

            # 委托回报信息
            "委托回报推送:",

            # 操作细节信息（新增）
            "开始买入操作",
            "需买入的股票:",
            "被过滤规则排除",
            "跳过买入",
            "可用资金:",
            "需买入股票数:",
            "最终买入数量:",
            "预计金额:",
            "当前持仓:",
            "可用:",
            "市值:",
            "记录每只股票的持仓情况",
            "使用定期日志",
            "板块.*初始股票列表:",
            "等共.*只股票",

            # 其他调试信息
            "完整数据:",
            "✅.*最新价格:",
            "当前持仓市值:",
            "预计买入后市值:",
            "目标总金额:",
            "剩余差额:",
            "预计在.*后继续买入",
            "直到达到目标金额"
        ]

        for pattern in filter_patterns:
            if re.search(pattern, message):
                return True
        return False

    def _should_suppress_error(self, message):
        """检查是否应该抑制重复错误"""
        import time
        current_time = time.time()

        # 检查是否是错误或警告消息
        if not (message.startswith('⚠️') or message.startswith('💡') or '错误' in message or '失败' in message):
            return False

        # 检查是否在抑制时间内
        if message in self._error_cache:
            if current_time - self._error_cache[message] < self._error_suppress_time:
                return True  # 抑制重复错误

        # 更新错误时间
        self._error_cache[message] = current_time
        return False  # 允许显示

    def _format_price(self, price):
        """格式化价格显示，处理浮点精度问题"""
        try:
            # 处理浮点精度问题
            if isinstance(price, (int, float)):
                # 四舍五入到2位小数
                formatted_price = round(float(price), 2)
                # 移除不必要的小数点后的0
                if formatted_price == int(formatted_price):
                    return str(int(formatted_price))
                else:
                    return f"{formatted_price:.2f}".rstrip('0').rstrip('.')
            return str(price)
        except:
            return str(price)

    def log_message(self, message, level="INFO"):
        """记录日志信息 - 增强版

        参数:
            message: 日志消息
            level: 日志级别，可选值为 "INFO", "WARNING", "ERROR", "DEBUG"
        """
        # 过滤掉不需要记录的日志
        if "买入数量无效" in message or "计算买入金额" in message:
            return

        try:
            # 优先使用增强日志管理器
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                try:
                    self.enhanced_log_manager.log_message(message, level)
                    return  # 成功则直接返回
                except Exception as e:
                    print(f"增强日志管理器失败: {e}")

            # 回退到安全日志方法
            self._safe_log_message(message, level)

        except Exception as e:
            # 最后的安全措施
            try:
                print(f"[{level}] {message}")
                print(f"日志记录失败: {e}")
            except:
                pass  # 避免无限递归

    def _update_simplified_log(self, log_entry):
        """更新简化日志显示（前台）"""
        try:
            if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                if self.simplified_log_text.winfo_exists():
                    timestamp = log_entry['timestamp'].strftime("%H:%M:%S")
                    message = log_entry['message']
                    formatted_message = f"[{timestamp}] {message}\n"

                    self.simplified_log_text.insert(tk.END, formatted_message)
                    self.simplified_log_text.see(tk.END)
        except Exception as e:
            # 静默处理异常，避免影响主流程
            pass

    def _update_detailed_log(self, log_entry):
        """更新详细日志显示（调试功能）"""
        try:
            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                if self.detailed_log_text.winfo_exists():
                    timestamp = log_entry['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
                    message = log_entry['message']
                    level = log_entry['level']
                    formatted_message = f"[{timestamp}] [{level}] {message}\n"

                    self.detailed_log_text.insert(tk.END, formatted_message)
                    self.detailed_log_text.see(tk.END)
        except Exception as e:
            # 静默处理异常，避免影响主流程
            pass

    def clear_simplified_log(self):
        """清空简化日志"""
        try:
            if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                self.simplified_log_text.delete(1.0, tk.END)

            # 如果增强日志管理器可用，也清空它
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                self.enhanced_log_manager.clear_simplified_logs()

            # 在详细日志中记录此操作
            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.detailed_log_text.insert(tk.END, f"[{current_time}] [INFO] 交易日志已清空\n")
                self.detailed_log_text.see(tk.END)

        except Exception as e:
            messagebox.showerror("错误", f"清空交易日志失败: {str(e)}")

    def clear_detailed_log(self):
        """清空详细日志"""
        try:
            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                self.detailed_log_text.delete(1.0, tk.END)

            # 如果增强日志管理器可用，也清空它
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                self.enhanced_log_manager.clear_detailed_logs()

            # 在简化日志中记录此操作
            if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                simple_time = datetime.now().strftime("%H:%M:%S")
                self.simplified_log_text.insert(tk.END, f"[{simple_time}] 详细日志已清空\n")
                self.simplified_log_text.see(tk.END)

        except Exception as e:
            messagebox.showerror("错误", f"清空详细日志失败: {str(e)}")

    def reset_stock_display(self):
        """重置股票显示状态"""
        try:
            # 如果增强日志管理器可用，重置它
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                self.enhanced_log_manager.clear_displayed_stocks()

            # 记录操作到日志
            self._safe_log_message("股票显示状态已重置，相同股票将重新显示", "INFO")

        except Exception as e:
            messagebox.showerror("错误", f"重置股票显示失败: {str(e)}")

    def export_logs(self):
        """导出日志"""
        try:
            from tkinter import filedialog

            # 选择导出类型
            export_type = messagebox.askyesnocancel("导出日志", "是：导出交易日志\n否：导出详细日志\n取消：取消操作")
            if export_type is None:
                return

            # 选择保存位置
            log_type_name = "交易日志" if export_type else "详细日志"
            default_filename = f"{log_type_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialname=default_filename
            )

            if filename:
                # 导出对应的日志内容
                if export_type:  # 导出交易日志
                    if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                        content = self.simplified_log_text.get(1.0, tk.END)
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(content)
                        messagebox.showinfo("成功", f"交易日志已导出到 {filename}")
                    else:
                        messagebox.showwarning("警告", "交易日志为空")
                else:  # 导出详细日志
                    if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                        content = self.detailed_log_text.get(1.0, tk.END)
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(content)
                        messagebox.showinfo("成功", f"详细日志已导出到 {filename}")
                    else:
                        messagebox.showwarning("警告", "详细日志为空")

        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {str(e)}")

    def show_log_stats(self):
        """显示日志统计信息"""
        try:
            # 统计当前日志内容
            simplified_lines = 0
            detailed_lines = 0

            if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                simplified_content = self.simplified_log_text.get(1.0, tk.END)
                simplified_lines = len([line for line in simplified_content.split('\n') if line.strip()])

            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                detailed_content = self.detailed_log_text.get(1.0, tk.END)
                detailed_lines = len([line for line in detailed_content.split('\n') if line.strip()])

            # 如果增强日志管理器可用，获取更详细的统计
            displayed_stocks = []
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                try:
                    stats = self.enhanced_log_manager.get_stats()
                    displayed_stocks = stats.get('displayed_stocks', [])
                except:
                    pass

            stats_message = f"""日志统计信息:

交易日志行数: {simplified_lines}
详细日志行数: {detailed_lines}
已显示股票数量: {len(displayed_stocks)}

已显示的股票代码:
{', '.join(displayed_stocks) if displayed_stocks else '暂无'}"""

            messagebox.showinfo("日志统计", stats_message)

        except Exception as e:
            messagebox.showerror("错误", f"获取日志统计失败: {str(e)}")

    def _setup_log_callbacks(self):
        """安全地设置日志回调函数"""
        try:
            if (hasattr(self, 'enhanced_log_manager') and
                self.enhanced_log_manager and
                hasattr(self, 'simplified_log_text') and
                hasattr(self, 'detailed_log_text')):

                self.enhanced_log_manager.add_frontend_callback(self._update_simplified_log)
                self.enhanced_log_manager.add_debug_callback(self._update_detailed_log)
                print("日志回调函数设置成功")
            else:
                print("日志回调函数设置失败：组件未准备好")
        except Exception as e:
            print(f"设置日志回调函数失败: {e}")

    def _update_time_cache(self):
        """更新时间缓存，线程安全"""
        try:
            with self._time_cache_lock:
                self._cached_start_time = self.start_time.get()
                self._cached_end_time = self.end_time.get()
        except Exception:
            # 如果获取失败，使用默认值
            pass

    def _get_cached_trading_times(self):
        """获取缓存的交易时间，线程安全"""
        with self._time_cache_lock:
            return self._cached_start_time, self._cached_end_time

    def _update_config_cache(self):
        """更新配置缓存，线程安全"""
        try:
            with self._config_cache_lock:
                self._cached_config.update({
                    'block_name': self.block_name_entry.get(),
                    'account': self.account_entry.get(),
                    'total_amount': self.total_amount_entry.get(),
                    'single_amount': self.single_amount_entry.get(),
                    'trade_interval': self.trade_interval_entry.get(),
                    'monitor_interval': self.monitor_interval_entry.get(),
                    'cancel_interval': self.cancel_interval_entry.get(),
                    'reserve_money': self.reserve_money_entry.get(),
                    'qmt_path': self.qmt_path_entry.get(),
                    'ths_path': self.ths_path_entry.get(),
                    'price_type': self.price_type.get()
                })
        except Exception:
            # 如果获取失败，使用默认值
            pass

    def _get_cached_config(self, key=None):
        """获取缓存的配置，线程安全"""
        with self._config_cache_lock:
            if key:
                return self._cached_config.get(key, '')
            else:
                return self._cached_config.copy()
        
    def log_periodic(self, message, level="INFO"):
        """定期记录日志信息，避免频繁输出
        
        参数:
            message: 日志消息
            level: 日志级别，可选值为 "INFO", "WARNING", "ERROR", "DEBUG"
        """
        current_time = datetime.now()
        # 如果距离上次日志输出时间超过设定的间隔，则输出日志
        if (current_time - self.last_log_time).total_seconds() >= self.log_interval:
            self.log_message(message, level)
            self.last_log_time = current_time

    def start_trading(self):
        """开始执行交易"""
        if not self.validate_inputs():
            return

        try:
            # 获取当前选择的板块名称
            block_name = self.block_name_entry.get()
            account = self.account_entry.get()
            total_amount = self.total_amount_entry.get()
            single_amount = self.single_amount_entry.get()
            trade_interval = self.trade_interval_entry.get()  # 已改为秒
            price_type = self.price_type.get()
            price_adjust = self.price_adjust.get()
            
            self.log_message(f"开始交易 - 账户: {account}, 板块: {block_name}, 总金额: {total_amount}, "
                            f"单笔金额: {single_amount}, 交易间隔: {trade_interval}秒, "
                            f"价格类型: {price_type}, 价格调整: {price_adjust}%")
            
            self.start_button.state(['disabled'])
            self.stop_button.state(['!disabled'])
            self.refresh_block_button.state(['disabled'])
            
            # 重置交易时间记录
            self.last_trade_time = {}

            # 更新时间缓存和配置缓存，确保线程安全
            self._update_time_cache()
            self._update_config_cache()

            # 更新状态显示
            self.status_label.config(text="运行中", foreground="green")

            # 启动交易线程
            self.trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
            self.trading_thread.start()
            
            self.log_message("交易系统启动成功")
        except Exception as e:
            self.log_message(f"启动交易失败: {str(e)}")
            messagebox.showerror("错误", f"启动交易失败: {str(e)}")
            self.stop_trading()

    def stop_trading(self):
        """启动后台线程执行停止操作，避免GUI阻塞"""
        try:
            # 立即更新UI状态，防止重复点击
            self.status_label.config(text="正在停止...", foreground="orange")
            self.stop_button.state(['disabled'])
            self.start_button.state(['disabled'])  # 防止在停止过程中启动
            self.log_message("正在停止交易系统...")

            # 在后台线程执行实际的停止操作
            stop_thread = threading.Thread(target=self._stop_trading_background, daemon=True)
            stop_thread.start()

        except Exception as e:
            self.log_message(f"启动停止操作失败: {str(e)}")
            # 恢复按钮状态
            self.start_button.state(['!disabled'])
            self.stop_button.state(['!disabled'])
            self.status_label.config(text="停止失败", foreground="red")

    def _stop_trading_background(self):
        """后台执行停止操作，避免阻塞GUI"""
        try:
            # 🔧 修复：设置系统停止状态，防止后续回调处理
            self.is_system_stopping = True

            # 停止调度器（改进版本）
            if self.scheduler:
                try:
                    # 🔧 修复：APScheduler的shutdown方法不支持timeout参数
                    # 先尝试优雅关闭，等待当前任务完成
                    self.scheduler.shutdown(wait=True)
                    self.root.after(0, lambda: self.log_message("调度器已优雅停止"))
                except Exception as e:
                    error_msg = str(e)  # 立即转换为字符串，避免闭包问题
                    try:
                        # 如果优雅关闭失败，强制关闭
                        self.scheduler.shutdown(wait=False)
                        self.root.after(0, lambda msg=error_msg: self.log_message(f"调度器强制停止: {msg}"))
                    except Exception as e2:
                        error_msg2 = str(e2)  # 立即转换为字符串
                        self.root.after(0, lambda msg=error_msg2: self.log_message(f"停止调度器失败: {msg}"))
                finally:
                    self.scheduler = None

            # 确保任务状态被正确重置
            try:
                # 重置任务执行状态
                self.is_monitoring_running = False
                # 注意：不再重新创建锁对象，因为这不能解决死锁问题
                # 锁的释放应该在任务完成时自动进行
                self.root.after(0, lambda: self.log_message("监控状态已重置"))
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"重置状态失败: {str(e)}"))

            # 关闭交易接口
            if self.xt_trader:
                try:
                    # 尝试撤销所有未成交订单（带超时控制）
                    self._cancel_pending_orders_with_timeout()

                    # 关闭交易接口
                    self.xt_trader = None
                    self.root.after(0, lambda: self.log_message("交易接口已关闭"))
                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"关闭交易接口失败: {str(e)}"))

            # 重置状态变量
            self.last_block_stocks = set()
            self.last_trade_time = {}  # 清空交易时间记录
            if hasattr(self, 'empty_block_logged'):
                delattr(self, 'empty_block_logged')

            # 在主线程中更新UI状态
            self.root.after(0, self._update_stop_ui_success)

        except Exception as e:
            # 在主线程中更新错误状态
            self.root.after(0, lambda: self._update_stop_ui_error(str(e)))

    def _cancel_pending_orders_with_timeout(self):
        """带超时控制的撤单操作"""
        import concurrent.futures

        try:
            account = self._get_cached_config('account')
            acc = StockAccount(account)

            # 使用线程池和超时控制查询订单
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self.xt_trader.query_stock_orders, acc, True)
                try:
                    orders = future.result(timeout=5)  # 5秒超时
                except concurrent.futures.TimeoutError:
                    self.root.after(0, lambda: self.log_message("查询未成交订单超时，跳过撤单操作"))
                    return

                if orders:
                    self.root.after(0, lambda: self.log_message(f"停止交易前撤销 {len(orders)} 个未成交订单"))

                    # 限制撤单数量，避免过长时间
                    max_cancel_orders = 50  # 最多撤销50个订单
                    orders_to_cancel = orders[:max_cancel_orders]

                    if len(orders) > max_cancel_orders:
                        self.root.after(0, lambda: self.log_message(f"订单数量过多，只撤销前{max_cancel_orders}个订单"))

                    # 批量撤单，每个操作带超时
                    for i, order in enumerate(orders_to_cancel):
                        try:
                            cancel_future = executor.submit(self.xt_trader.cancel_order_stock, acc, order.order_id)
                            cancel_future.result(timeout=2)  # 每个撤单操作2秒超时

                            # 每10个订单暂停一下，避免过于频繁
                            if (i + 1) % 10 == 0:
                                time.sleep(0.1)

                        except concurrent.futures.TimeoutError:
                            self.root.after(0, lambda: self.log_message(f"撤销订单 {order.order_id} 超时"))
                        except Exception as e:
                            self.root.after(0, lambda: self.log_message(f"撤销订单 {order.order_id} 失败: {str(e)}"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"撤销未成交订单失败: {str(e)}"))

    def _update_stop_ui_success(self):
        """成功停止后更新UI状态"""
        self.start_button.state(['!disabled'])
        self.stop_button.state(['disabled'])
        self.refresh_block_button.state(['!disabled'])
        self.status_label.config(text="已停止", foreground="red")
        self.log_message("交易系统已完全停止")

        # 🔧 修复：重置系统停止状态标志
        self.is_system_stopping = False

    def _update_stop_ui_error(self, error_msg):
        """停止失败后更新UI状态"""
        self.log_message(f"停止交易失败: {error_msg}")
        self.status_label.config(text="停止失败", foreground="red")

        # 确保按钮状态正确
        self.start_button.state(['!disabled'])
        self.stop_button.state(['disabled'])
        self.refresh_block_button.state(['!disabled'])

    def validate_inputs(self):
        # 验证必填字段时需要判断是否是默认提示文本
        required_fields = {
            '证券账号': self.account_entry.get(),
            'QMT路径': self.get_entry_value(self.qmt_path_entry, "示例：D:\\国金QMT交易端模拟\\userdata_mini"),
            '同花顺板块目录': self.get_entry_value(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686"),
            '板块名称': self.get_entry_value(self.block_name_entry, "输入板块名称")
        }
        
        for field_name, value in required_fields.items():
            if not value.strip():
                messagebox.showerror("错误", f"{field_name}不能为空")
                return False
                
        # 验证同花顺目录是否存在
        ths_path = self.get_entry_value(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686")
        if not os.path.exists(ths_path):
            messagebox.showerror("错误", "同花顺目录不存在")
            return False
            
        # 验证StockBlock.ini文件是否存在
        stock_block_file = os.path.join(ths_path, "StockBlock.ini")
        if not os.path.exists(stock_block_file):
            messagebox.showerror("错误", "同花顺板块文件(StockBlock.ini)不存在，请确认目录是否正确")
            return False
            
        # 验证板块是否存在
        block_name = self.get_entry_value(self.block_name_entry, "输入板块名称")
        try:
            stocks = get_stocks_by_block_name(block_name, ths_path)
            if not stocks:
                self.log_message(f"警告: 板块 {block_name} 中没有股票")
                if not messagebox.askyesno("警告", f"板块 {block_name} 中没有股票，是否继续?"):
                    return False
        except Exception as e:
            messagebox.showerror("错误", f"读取板块失败: {str(e)}")
            return False

        # 验证交易间隔
        is_valid, error_msg = self.validate_trade_interval()
        if not is_valid:
            messagebox.showerror("错误", f"交易间隔设置错误: {error_msg}")
            return False

        return True

    def trading_loop(self):
        try:
            # 初始化交易对象
            session_id = random.randint(100000, 999999)
            qmt_path = self._get_cached_config('qmt_path')
            self.xt_trader = XtQuantTrader(qmt_path, session_id)
            self.xt_trader.start()

            # 注册回调
            callback = MyXtQuantTraderCallback(self.log_message)
            self.xt_trader.register_callback(callback)

            # 连接交易服务器
            if self.xt_trader.connect() != 0:
                raise Exception("连接交易服务器失败")

            # 订阅账户（使用缓存的配置）
            account = self._get_cached_config('account')
            acc = StockAccount(account)
            if self.xt_trader.subscribe(acc) != 0:
                raise Exception("订阅账户失败")

            # 连接数据服务
            xtdata.connect()

            # 初始化调度器
            self.init_scheduler()
            
            self.log_message("交易循环启动成功")
            self.log_message("交易顺序：先卖出再买入，确保有足够资金进行交易")
            
        except Exception as e:
            self.log_message(f"交易循环初始化失败: {str(e)}")
            self.stop_trading()

    def init_scheduler(self):
        """初始化调度器"""
        try:
            # 如果已存在调度器，先关闭它
            if self.scheduler:
                self.scheduler.shutdown(wait=False)

            # 从配置文件读取间隔设置
            monitor_seconds, cancel_seconds = self.get_monitor_intervals_from_config()

            # 验证间隔范围
            monitor_seconds = max(1, min(3600, monitor_seconds))  # 1秒-60分钟
            cancel_seconds = max(5, min(1800, cancel_seconds))    # 5秒-30分钟

            # 创建新的调度器
            executor_config = {
                'executors': {
                    'default': ThreadPoolExecutor(max_workers=20)
                },
                'job_defaults': {
                    'coalesce': True,
                    'max_instances': 3,
                    'misfire_grace_time': 30  # 允许任务延迟执行的最大秒数
                }
            }

            self.scheduler = BackgroundScheduler(executor_config)

            # 添加监控任务 - 使用配置的间隔
            self.scheduler.add_job(
                self.monitor_job,
                'interval',
                seconds=monitor_seconds,
                id='monitor_job',
                replace_existing=True,
                max_instances=1  # 确保同时只有一个实例在运行
            )

            # 添加定时撤单任务 - 使用配置的间隔
            self.scheduler.add_job(
                self.cancel_orders_wrapper,
                'interval',
                seconds=cancel_seconds,
                id='cancel_orders',
                replace_existing=True,
                max_instances=1  # 确保同时只有一个实例在运行
            )

            # 启动调度器
            self.scheduler.start()

            # 显示当前间隔设置
            monitor_display = f"{monitor_seconds}秒" if monitor_seconds < 60 else f"{monitor_seconds//60}分钟"
            cancel_display = f"{cancel_seconds}秒" if cancel_seconds < 60 else f"{cancel_seconds//60}分钟"

            self.log_message(f"调度器已启动，板块监控间隔为{monitor_display}，撤单任务间隔为{cancel_display}")

        except Exception as e:
            self.log_message(f"初始化调度器失败: {str(e)}")
            raise

    def monitor_job(self):
        """定时任务：监控板块文件变化并执行交易
        
        该方法会被定时任务系统定期调用，实现以下功能：
        1. 读取同花顺板块文件，获取当前板块股票列表
        2. 根据持仓情况，确定需要买入和卖出的股票
        3. 根据交易规则执行买入和卖出操作
        4. 处理各种异常情况，确保交易稳定执行
        
        交易规则：
        - 买入：当股票在板块内且持仓市值小于目标金额时买入
        - 卖出：
          - 非板块股票：分批卖出直到清仓
          - 板块内股票：不进行卖出，即使超过目标金额
        - 所有交易遵循设置的交易间隔和单笔金额限制
        """
        # 使用超时锁机制，避免永久死锁
        try:
            # 尝试获取锁，最多等待1秒
            if self.monitor_lock.acquire(timeout=1):
                try:
                    self.is_monitoring_running = True
                    self.monitor_file()
                except Exception as e:
                    self.log_message(f"监控文件异常: {str(e)}")
                    # 发生异常时，显示完整的异常堆栈信息，方便调试
                    self.log_message(traceback.format_exc())
                finally:
                    self.is_monitoring_running = False
                    try:
                        self.monitor_lock.release()
                    except Exception:
                        # 如果释放锁失败，记录但不阻塞
                        pass
            else:
                self.log_periodic("上一个监控任务仍在执行中，跳过本次执行")
        except Exception as e:
            self.log_message(f"监控任务包装器异常: {str(e)}")
            self.is_monitoring_running = False

    def monitor_file(self):
        """监控同花顺板块变化并执行交易
        
        整体交易逻辑：
        1. 读取同花顺板块中的股票列表
        2. 获取当前持仓和未成交委托单
        3. 找出需要卖出的股票（只包括非板块中的持仓）
        4. 找出需要买入的股票（板块内但持仓不足目标金额的股票）
        5. 先执行卖出操作，释放资金
        6. 再执行买入操作，确保资金合理利用
        7. 板块内股票不进行卖出操作，无论持仓是否超过目标金额
        """
        try:
            # 检查交易日期变化（每次监控开始时检查）
            self._check_trade_date_change()

            # 获取缓存的配置，避免在后台线程中访问GUI组件
            block_name = self._get_cached_config('block_name')
            ths_path = self._get_cached_config('ths_path')

            # 使用read_block.py中的函数获取板块股票
            current_block_stocks = set(get_stocks_by_block_name(block_name, ths_path))

            if not current_block_stocks:
                # 只在首次或有变化时记录日志
                if not hasattr(self, 'empty_block_logged') or not self.empty_block_logged:
                    self.log_message(f"板块 {block_name} 中没有股票")
                    self.empty_block_logged = True
                return
            else:
                self.empty_block_logged = False

            # 检查板块内容是否有变化
            if current_block_stocks == self.last_block_stocks:
                # 即使板块内容没有变化，也需要检查是否有需要继续买入的股票
                self.log_periodic(f"监控板块 {block_name} 中的 {len(current_block_stocks)} 只股票")
            else:
                # 记录变化情况
                if self.last_block_stocks:
                    added = current_block_stocks - self.last_block_stocks
                    removed = self.last_block_stocks - current_block_stocks
                    if added:
                        self.log_message(f"板块新增股票: {', '.join(added)}")
                    if removed:
                        self.log_message(f"板块移除股票: {', '.join(removed)}")
                else:
                    stock_display = ', '.join(list(current_block_stocks)[:10])
                    if len(current_block_stocks) > 10:
                        stock_display += f"... 等共{len(current_block_stocks)}只股票"
                    self.log_message(f"板块 {block_name} 初始股票列表: {stock_display}")

                self.last_block_stocks = current_block_stocks

            # 获取当前持仓（使用缓存的账号配置）
            account = self._get_cached_config('account')
            acc = StockAccount(account)
            positions = self.xt_trader.query_stock_positions(acc)
            position_stocks = {pos.stock_code: pos for pos in positions if pos.volume > 0}  # 只考虑持仓数量大于0的股票
            
            # 获取当前未成交委托单
            pending_orders = self.xt_trader.query_stock_orders(acc, True)  # True表示只查询未完成订单
            pending_order_stocks = {order.stock_code for order in pending_orders}
            
            # 获取最新价格和计算持仓市值（使用缓存的配置）
            latest_prices = {}
            position_values = {}
            total_amount = float(self._get_cached_config('total_amount'))
            
            # 计算所有板块内股票的最新价格和持仓市值
            for stock_code in current_block_stocks:
                latest_price = self.get_latest_price(stock_code)
                if latest_price > 0:
                    latest_prices[stock_code] = latest_price
                    
                    # 计算持仓市值
                    if stock_code in position_stocks:
                        position = position_stocks[stock_code]
                        position_values[stock_code] = position.volume * latest_price
                    else:
                        position_values[stock_code] = 0
            
            # 找出需要买入的股票（包括持仓不足目标金额的股票）
            stocks_to_buy = set()
            # 用于收集交易间隔未到的股票
            interval_not_reached_stocks = []
            
            for stock_code in current_block_stocks:
                if stock_code in pending_order_stocks:
                    continue  # 跳过已有未成交委托的股票
                
                if stock_code not in position_values:
                    continue  # 跳过无法获取价格的股票
                
                position_value = position_values[stock_code]
                
                # 检查交易间隔（使用缓存的配置）
                current_time = datetime.now()
                trade_interval = float(self._get_cached_config('trade_interval'))
                can_trade = True
                
                if stock_code in self.last_trade_time:
                    last_time = self.last_trade_time[stock_code]
                    elapsed_seconds = (current_time - last_time).total_seconds()
                    
                    if elapsed_seconds < trade_interval:
                        can_trade = False
                        # 收集交易间隔未到的股票，而不是立即输出日志
                        interval_not_reached_stocks.append((stock_code, elapsed_seconds))
                
                # 如果持仓市值小于目标金额且满足交易间隔，则加入买入列表
                if position_value < total_amount and can_trade:
                    # 检查股票过滤规则
                    if not self.is_stock_filtered(stock_code):
                        stocks_to_buy.add(stock_code)
                    else:
                        self.log_periodic(f"{stock_code}被过滤规则排除，跳过买入")
            
            # 如果有交易间隔未到的股票，汇总输出一条日志
            if interval_not_reached_stocks:
                # 只显示前5只股票的详细信息，其余的只显示数量
                if len(interval_not_reached_stocks) <= 5:
                    stocks_info = ", ".join([f"{code}({elapsed:.1f}秒)" for code, elapsed in interval_not_reached_stocks])
                    self.log_periodic(f"有{len(interval_not_reached_stocks)}只股票未达到交易间隔{trade_interval}秒: {stocks_info}")
                else:
                    stocks_info = ", ".join([f"{code}({elapsed:.1f}秒)" for code, elapsed in interval_not_reached_stocks[:5]])
                    self.log_periodic(f"有{len(interval_not_reached_stocks)}只股票未达到交易间隔{trade_interval}秒，前5只: {stocks_info}等")
            
            # 检查是否禁用卖出功能
            if self.disable_sell.get():
                self.log_periodic("卖出功能已禁用，跳过所有卖出操作")
                stocks_to_sell = set()
            else:
                # 找出需要卖出的股票（不在板块中的持仓股票）
                stocks_to_sell = set()
                for stock_code in set(position_stocks.keys()) - current_block_stocks - pending_order_stocks:
                    position = position_stocks[stock_code]
                    # 检查T+1限制，只有当可用数量大于0时才添加到卖出列表
                    if position.can_use_volume > 0:
                        stocks_to_sell.add(stock_code)
                    else:
                        self.log_periodic(f"{stock_code}为T+1产品，当前无可用余额，跳过卖出")
            
            # 用于收集卖出交易间隔未到的股票
            sell_interval_not_reached_stocks = []
            
            # 检查持仓中需要卖出的股票（持仓市值超过目标金额）
            # 根据要求，板块内产品不进行卖出，无论是否超过目标金额
            # 注释掉以下代码，不再处理板块内超额持仓的卖出
            '''
            for stock_code in current_block_stocks & set(position_stocks.keys()):
                if stock_code in pending_order_stocks:
                    continue  # 跳过已有未成交委托的股票
                
                position = position_stocks[stock_code]
                # 检查T+1限制，如果可用数量为0则跳过
                if position.can_use_volume <= 0:
                    self.log_periodic(f"{stock_code}为T+1产品，当前无可用余额，跳过卖出")
                    continue
                
                latest_price = self.get_latest_price(stock_code)
                if latest_price <= 0:
                    continue  # 跳过无法获取价格的股票
                    
                current_position_value = position.volume * latest_price
                
                # 如果持仓市值超过目标金额，检查交易间隔
                if current_position_value > total_amount:
                    current_time = datetime.now()
                    trade_interval = float(self._get_cached_config('trade_interval'))
                    can_trade = True
                    
                    if stock_code in self.last_trade_time:
                        last_time = self.last_trade_time[stock_code]
                        elapsed_seconds = (current_time - last_time).total_seconds()
                        
                        if elapsed_seconds < trade_interval:
                            can_trade = False
                            # 收集交易间隔未到的股票
                            sell_interval_not_reached_stocks.append((stock_code, elapsed_seconds))
                    
                    if can_trade:
                        stocks_to_sell.add(stock_code)
            '''
            
            # 如果有卖出交易间隔未到的股票，汇总输出一条日志
            if sell_interval_not_reached_stocks:
                # 只显示前5只股票的详细信息，其余的只显示数量
                if len(sell_interval_not_reached_stocks) <= 5:
                    stocks_info = ", ".join([f"{code}({elapsed:.1f}秒)" for code, elapsed in sell_interval_not_reached_stocks])
                    self.log_periodic(f"有{len(sell_interval_not_reached_stocks)}只股票未达到卖出交易间隔{trade_interval}秒: {stocks_info}")
                else:
                    stocks_info = ", ".join([f"{code}({elapsed:.1f}秒)" for code, elapsed in sell_interval_not_reached_stocks[:5]])
                    self.log_periodic(f"有{len(sell_interval_not_reached_stocks)}只股票未达到卖出交易间隔{trade_interval}秒，前5只: {stocks_info}等")
            
            # 🔧 修复：增强资金查询，添加详细日志和验证
            try:
                assets = self.xt_trader.query_stock_asset(acc)
                if not assets:
                    self.log_message("❌ 资金查询返回空结果")
                    return

                available_money = float(assets.cash) if hasattr(assets, 'cash') else 0
                total_asset = float(assets.total_asset) if hasattr(assets, 'total_asset') else 0
                frozen_cash = float(assets.frozen_cash) if hasattr(assets, 'frozen_cash') else 0
                reserve_money = float(self._get_cached_config('reserve_money'))

                # 🔧 修复：移除详细资金日志，避免刷屏
                # 只在资金异常时才输出详细信息

                # 资金合理性验证
                if available_money < 0:
                    self.log_message(f"⚠️ 可用资金异常: {available_money:.2f}元")
                if available_money > total_asset * 1.5:  # 可用资金异常高于总资产
                    self.log_message(f"⚠️ 可用资金({available_money:.2f})异常高于总资产({total_asset:.2f})")
                
                # 检查交易时间（使用线程安全的方法）
                current_time = datetime.now().strftime("%H:%M:%S")
                is_trading_time = self.is_trading_time(current_time)
                
                # 过滤掉无可用余额的股票，避免重复检查
                real_stocks_to_sell = []
                for stock_code in stocks_to_sell:
                    position = position_stocks.get(stock_code)
                    if position and position.can_use_volume > 0:
                        real_stocks_to_sell.append(stock_code)
                
                # 执行卖出 - 只在交易时间内卖出，且有实际可卖出股票
                """
                卖出逻辑：
                1. 对于非板块股票：分批卖出直到清仓
                2. 对于板块内股票：不进行卖出操作，无论持仓是否超过目标金额
                3. 每次卖出受单笔金额限制，确保分批卖出
                4. 每只股票卖出后需要等待交易间隔才能再次卖出
                """
                if is_trading_time and real_stocks_to_sell:
                    self.log_message(f"开始卖出操作，需卖出股票数: {len(real_stocks_to_sell)}")
                    self.log_message(f"需卖出的股票: {', '.join(real_stocks_to_sell[:10])}{' 等' if len(real_stocks_to_sell) > 10 else ''}")
                    for stock_code in real_stocks_to_sell:
                        self.place_sell_order(stock_code)
                        
                        # 卖出后更新可用资金
                        assets = self.xt_trader.query_stock_asset(acc)
                        available_money = assets.cash
                else:
                    # 使用定期日志，避免频繁输出
                    if not is_trading_time:
                        start_time, end_time = self._get_cached_trading_times()
                        self.log_periodic(f"当前时间 {current_time} 不在交易时间范围内 ({start_time} - {end_time})")
                    elif not real_stocks_to_sell:
                        self.log_periodic("没有需要卖出的股票或股票无可用余额")
                
                # 执行买入 - 只在交易时间内且资金充足时买入
                """
                买入逻辑：
                1. 只买入板块内股票
                2. 只买入持仓市值低于目标金额的股票
                3. 受单笔金额限制，确保分批买入
                4. 每只股票买入后需要等待交易间隔才能再次买入
                5. 保证可用资金不低于保留资金设置
                """
                if is_trading_time and available_money > reserve_money and stocks_to_buy:
                    # 过滤当日已卖出的股票
                    original_count = len(stocks_to_buy)
                    if self.enable_sold_stock_filter:
                        stocks_to_buy = {stock for stock in stocks_to_buy if not self._is_stock_sold_today(stock)}
                        filtered_count = original_count - len(stocks_to_buy)

                        if filtered_count > 0:
                            self.log_message(f"🚫 已过滤 {filtered_count} 只当日卖出的股票，剩余 {len(stocks_to_buy)} 只可买入")

                            # 记录被过滤的股票
                            sold_stocks_in_buy_list = [stock for stock in current_block_stocks if self._is_stock_sold_today(stock)]
                            if sold_stocks_in_buy_list:
                                self.log_message(f"📝 当日已卖出股票: {', '.join(sold_stocks_in_buy_list[:5])}{' 等' if len(sold_stocks_in_buy_list) > 5 else ''}")

                    if stocks_to_buy:
                        self.log_message(f"开始买入操作，可用资金: {available_money:.2f}，需买入股票数: {len(stocks_to_buy)}")
                        self.log_message(f"需买入的股票: {', '.join(list(stocks_to_buy)[:10])}{' 等' if len(stocks_to_buy) > 10 else ''}")

                        # 记录每只股票的持仓情况 - 使用定期日志
                        for stock_code in stocks_to_buy:
                            if stock_code in position_values:
                                position_value = position_values[stock_code]
                                self.log_periodic(f"{stock_code}当前持仓市值: {position_value:.2f}，目标总金额: {total_amount:.2f}，差额: {total_amount - position_value:.2f}")

                        for stock_code in stocks_to_buy:
                            self.place_buy_order(stock_code, available_money, reserve_money)
                    else:
                        self.log_message("ℹ️ 所有待买入股票都已在当日卖出，跳过买入操作")
                else:
                    # 使用定期日志，避免频繁输出
                    if not is_trading_time:
                        start_time, end_time = self._get_cached_trading_times()
                        self.log_periodic(f"当前时间 {current_time} 不在交易时间范围内 ({start_time} - {end_time})")
                    elif available_money <= reserve_money:
                        self.log_periodic(f"可用资金 {available_money:.2f} 小于等于保留资金 {reserve_money:.2f}")
                    elif not stocks_to_buy:
                        self.log_periodic("没有需要买入的股票")
            
            except Exception as e:
                self.log_message(f"交易执行失败: {str(e)}")
            
        except Exception as e:
            block_name = self._get_cached_config('block_name')
            self.log_message(f"监控板块失败 {block_name}: {str(e)}")
            # 记录详细错误信息以便调试
            self.log_message(traceback.format_exc())

    def cancel_orders_wrapper(self):
        """
        撤单函数的包装器，确保上一次撤单任务完成后才能执行下一次
        """
        # 使用非阻塞方式获取锁，避免阻塞调度器线程
        if self.monitor_lock.acquire(False):  # 非阻塞方式获取锁
            try:
                # 执行实际的撤单任务
                self.cancel_pending_orders()
            except Exception as e:
                self.log_message(f"撤单任务出错: {str(e)}")
                self.log_message(traceback.format_exc())
            finally:
                # 释放锁，允许其他任务执行
                self.monitor_lock.release()
        else:
            # 如果获取锁失败，说明有其他任务正在执行
            self.log_periodic("当前有其他任务正在执行，跳过本次撤单")

    def cancel_pending_orders(self):
        """定期撤销未成交订单"""
        try:
            # 检查交易时间
            current_time = datetime.now().strftime("%H:%M:%S")
            if not self.is_trading_time(current_time):
                return
                
            account = self._get_cached_config('account')
            acc = StockAccount(account)
            orders = self.xt_trader.query_stock_orders(acc, True)  # True表示只查询未完成订单
            
            if not orders:
                return
                
            current_time = datetime.now().timestamp()
            orders_to_cancel = []
            
            for order in orders:
                # 检查订单是否超过10秒未成交
                order_time = order.order_time if hasattr(order, 'order_time') else 0
                if current_time - order_time > 10:  # 超过10秒的订单
                    orders_to_cancel.append(order)
            
            if orders_to_cancel:
                self.log_message(f"发现 {len(orders_to_cancel)} 个超过10秒未成交订单，准备撤单")
                for order in orders_to_cancel:
                    cancel_result = self.xt_trader.cancel_order_stock(acc, order.order_id)
                    if cancel_result == 0:
                        self.log_message(f"撤单成功：{order.stock_code}，订单号：{order.order_id}")
                    else:
                        self.log_message(f"撤单失败：{order.stock_code}，订单号：{order.order_id}")
                
                # 撤单后不需要在这里调用monitor_file，因为调度器会定期触发monitor_wrapper
                # 获取板块名称，记录日志即可
                block_name = self.get_entry_value(self.block_name_entry, "输入板块名称")
                self.log_message(f"已撤单完成，等待下一次监控任务自动执行")
                
        except Exception as e:
            self.log_message(f"撤单操作失败: {str(e)}")

    def place_buy_order(self, stock_code, available_money, reserve_money):
        """执行买入委托

        买入策略：
        1. 股票必须在同花顺板块内
        2. 根据当前持仓市值和目标金额，计算买入数量
        3. 单次买入金额不超过设置的单笔金额
        4. 买入后总持仓不超过目标金额
        5. 买入委托遵循设置的价格类型（限价、对手方等）
        6. A股按100股整数倍买入，可转债按10张整数倍买入
        """
        try:
            # 🔧 增强：添加交易时间检查，确保严格的时间控制
            current_time = datetime.now().strftime("%H:%M:%S")
            if not self.is_trading_time(current_time):
                return  # 非交易时间不执行买入

            account = self._get_cached_config('account')
            acc = StockAccount(account)
            
            # 检查可用资金是否足够
            if available_money <= reserve_money:
                self.log_message(f"可用资金({available_money:.2f})小于等于保留资金({reserve_money:.2f})，跳过买入")
                return

            # 获取最新价格
            latest_price = self.get_latest_price(stock_code)
            if latest_price <= 0:
                self.log_message(f"获取{stock_code}最新价格失败")
                return

            # 🚨 紧急添加：最终价格验证（双重保护）
            if latest_price > 0:
                price_valid, price_error = self._validate_price_reasonableness(stock_code, latest_price, stock_code)
                if not price_valid:
                    self.log_message(f"买入前价格验证失败 {stock_code}: {price_error}")
                    self.log_message(f"为防止交易损失，取消买入操作")
                    return 0
                
            # 检查交易间隔 - 在monitor_file中已经检查过，这里不再重复输出日志
            current_time = datetime.now()
            trade_interval = float(self._get_cached_config('trade_interval'))
            
            if stock_code in self.last_trade_time:
                last_time = self.last_trade_time[stock_code]
                elapsed_seconds = (current_time - last_time).total_seconds()
                
                if elapsed_seconds < trade_interval:
                    # 不再输出日志，直接返回
                    return
            
            # 获取当前持仓
            positions = self.xt_trader.query_stock_positions(acc)
            current_position = None
            for pos in positions:
                if pos.stock_code == stock_code and pos.volume > 0:  # 确保只考虑持仓数量大于0的股票
                    current_position = pos
                    break
            
            # 计算当前持仓市值
            current_position_value = 0
            if current_position:
                current_position_value = current_position.volume * latest_price
                # 使用定期日志，避免频繁输出
                self.log_periodic(f"{stock_code}当前持仓: {current_position.volume}股，市值: {current_position_value:.2f}元")
            else:
                # 使用定期日志，避免频繁输出
                self.log_periodic(f"{stock_code}当前无持仓或持仓为0")
            
            # 获取单只总金额（使用缓存的配置）
            total_amount = float(self._get_cached_config('total_amount'))

            # 计算还需要买入的金额
            remaining_amount = total_amount - current_position_value

            if remaining_amount <= 0:
                # 使用定期日志，避免频繁输出
                self.log_periodic(f"{stock_code}当前持仓市值({current_position_value:.2f})已达到或超过单只总金额({total_amount:.2f})")
                return

            # 🔧 修复：使用精确的买入金额计算
            volume, buy_amount_precise, calc_message = self._calculate_buy_amount_precise(
                stock_code, latest_price, available_money, reserve_money
            )

            if volume <= 0:
                self.log_message(f"⚠️ {stock_code}无法买入: {calc_message}")
                if "资金不足" in calc_message:
                    # 提取最小需要金额进行更详细的提示
                    single_amount = float(self._get_cached_config('single_amount'))
                    min_unit = 100 if not stock_code.startswith(('11', '12')) else 10
                    min_amount_needed = min_unit * latest_price
                    self.log_message(f"💡 建议: 将单笔金额设置为至少{min_amount_needed:.0f}元，或选择价格更低的股票")
                return

            # 🔧 修复：简化买入日志，只显示关键信息
            # self.log_message(f"{stock_code}最终买入数量: {volume}股，预计金额: {volume * latest_price:.2f}元")

            # 设置委托价格和类型
            price_type = self.price_type.get()
            price = latest_price  # 默认使用最新价，避免0价格风险
            adjust_percent = float(self.price_adjust.get()) / 100

            # 🔧 修复：移除详细价格计算日志，避免刷屏
            # 只在价格异常时才输出详细信息

            if price_type == "限价":
                price = latest_price * (1 + adjust_percent)  # 买入时加上价格调整的百分比
                price = self._round_price(price, stock_code)
                self.log_message(f"   限价委托价格: {latest_price} × (1 + {adjust_percent}) = {price}")
            elif price_type == "对手方最优":
                # 对手方最优市价单，使用调整后的价格作为参考价格，避免0价格风险
                price = latest_price * (1 + adjust_percent)
                price = self._round_price(price, stock_code)
                self.log_message(f"   对手方最优参考价格: {latest_price} × (1 + {adjust_percent}) = {price}")
            elif price_type == "本方最优":
                # 本方最优市价单，使用最新价作为参考价格
                price = latest_price
                self.log_message(f"   本方最优参考价格: {price}")
            elif price_type == "最优五档":
                # 获取五档数据并选择最优价格
                five_level_data = self._get_five_level_data(stock_code)
                if five_level_data:
                    self._log_five_level_info(stock_code, five_level_data)
                    price = self._calculate_five_level_price_with_adjustment(
                        five_level_data, is_buy=True, adjust_percent=adjust_percent)
                    price = self._round_price(price, stock_code)
                    self.log_message(f"   最优五档买入价格: {price}")
                else:
                    # 获取五档数据失败，降级为对手方最优
                    price = latest_price * (1 + adjust_percent)
                    price = self._round_price(price, stock_code)
                    self.log_message(f"   五档数据获取失败，降级为参考价格: {price}")
            elif price_type == "最优五档转限价":
                # 检查是否为可转债
                if stock_code.startswith(('11', '12')):  # 可转债
                    # 可转债直接使用最新价进行限价委托
                    price = latest_price
                    price_type = "限价"
                    self.log_message(f"   可转债限价委托价格: {price}")
                else:
                    # 获取五档数据
                    five_level_data = self._get_five_level_data(stock_code)
                    if five_level_data:
                        self._log_five_level_info(stock_code, five_level_data)
                        # 尝试五档委托
                        best_price, level = self._select_best_five_level_price(
                            five_level_data, is_buy=True, min_volume=100)

                        if level > 0:
                            # 五档有合适价格，使用五档委托
                            price = best_price
                            self.log_message(f"   五档转限价，使用第{level}档价格: {price}")
                            # 保持原有的五档委托类型
                        else:
                            # 五档无合适价格，转为限价委托
                            price = latest_price * (1 + adjust_percent)
                            price = self._round_price(price, stock_code)
                            price_type = "限价"
                            self.log_message(f"   五档无合适价格，转限价委托: {price}")
                    else:
                        # 获取五档数据失败，直接转限价
                        price = latest_price * (1 + adjust_percent)
                        price = self._round_price(price, stock_code)
                        price_type = "限价"
                        self.log_message(f"   获取五档失败，转限价委托: {price}")

            # 最终价格安全检查，确保价格不为0
            if price <= 0:
                price = latest_price
                self.log_message(f"⚠️ {stock_code}价格异常，使用最新价作为安全价格：{price}")

            # 验证价格是否在涨跌停范围内
            price = self._validate_price_range(price, latest_price, stock_code, is_buy=True)

            self.log_message(f"   最终委托价格: {price}")

            # 处理五档转限价的动态类型转换
            final_price_type = price_type
            # 注意：这里的price_type可能在上面的逻辑中被修改为"限价"

            price_type_map = {
                "限价": xtconstant.FIX_PRICE,
                "最优五档": xtconstant.MARKET_SH_CONVERT_5_CANCEL if stock_code.endswith("SH")
                           else xtconstant.MARKET_SZ_CONVERT_5_CANCEL,
                "最优五档转限价": xtconstant.FIX_PRICE if final_price_type == "限价" else (
                    xtconstant.MARKET_SH_CONVERT_5_CANCEL if stock_code.endswith("SH")
                    else xtconstant.MARKET_SZ_CONVERT_5_CANCEL),
                "对手方最优": xtconstant.MARKET_PEER_PRICE_FIRST,
                "本方最优": xtconstant.MARKET_MINE_PRICE_FIRST
            }

            xt_price_type = price_type_map.get(final_price_type, xtconstant.MARKET_PEER_PRICE_FIRST)
            
            order_id = self.xt_trader.order_stock(
                acc,
                stock_code,
                xtconstant.STOCK_BUY,
                volume,
                xt_price_type,
                price,
                "同花顺板块委托单",
                price_type
            )
            
            # 更新最后交易时间
            self.last_trade_time[stock_code] = current_time
            
            # 计算预计买入后的持仓市值
            expected_position_value = current_position_value + (volume * latest_price)
            expected_remaining = total_amount - expected_position_value
            
            # 根据委托类型显示不同的日志信息
            if price_type in ["对手方最优", "本方最优", "最优五档"]:
                self.log_message(f"买入委托成功: {stock_code}, 数量: {volume}, 类型: {price_type}(市价), 参考价格: {price}, 订单号: {order_id}")
            else:
                self.log_message(f"买入委托成功: {stock_code}, 数量: {volume}, 价格: {price}, 订单号: {order_id}")
            self.log_message(f"当前持仓市值: {current_position_value:.2f}, 预计买入后市值: {expected_position_value:.2f}, 目标总金额: {total_amount:.2f}, 剩余差额: {expected_remaining:.2f}")
            
            # 如果预计买入后仍未达到目标金额，提示将在交易间隔后继续买入
            if expected_remaining > 0:
                next_trade_time = current_time + timedelta(seconds=trade_interval)
                self.log_periodic(f"{stock_code}预计在 {next_trade_time.strftime('%Y-%m-%d %H:%M:%S')} 后继续买入，直到达到目标金额 {total_amount:.2f}")
            
        except Exception as e:
            self.log_message(f"买入委托失败 {stock_code}: {str(e)}")

    def place_sell_order(self, stock_code):
        """执行卖出委托

        卖出策略：
        1. 根据股票是否在板块内采用不同策略：
           - 板块内股票：不进行卖出操作，即使超过目标金额
           - 非板块股票：分批卖出直到清仓
        2. 单次卖出金额不超过设置的单笔金额
        3. 遵循T+1交易规则，只卖出可用股份
        4. 卖出委托遵循设置的价格类型（限价、对手方等）
        5. A股按100股整数倍卖出，可转债按10张整数倍卖出
        """
        try:
            # 🔧 增强：添加交易时间检查，确保严格的时间控制
            current_time = datetime.now().strftime("%H:%M:%S")
            if not self.is_trading_time(current_time):
                return  # 非交易时间不执行卖出

            account = self._get_cached_config('account')
            acc = StockAccount(account)

            # 判断是否属于当前板块
            is_in_block = stock_code in self.last_block_stocks

            # 板块内股票不进行卖出
            if is_in_block:
                self.log_periodic(f"{stock_code}在当前板块中，根据设置不进行卖出")
                return

            # 获取最新价格
            latest_price = self.get_latest_price(stock_code)
            if latest_price <= 0:
                self.log_message(f"获取{stock_code}最新价格失败")
                return

            # 检查交易间隔 - 在monitor_file中已经检查过，这里不再重复输出日志
            current_time = datetime.now()
            trade_interval = float(self._get_cached_config('trade_interval'))
            
            if stock_code in self.last_trade_time:
                last_time = self.last_trade_time[stock_code]
                elapsed_seconds = (current_time - last_time).total_seconds()
                
                if elapsed_seconds < trade_interval:
                    # 不再输出日志，直接返回
                    return
            
            # 获取当前持仓
            positions = self.xt_trader.query_stock_positions(acc)
            current_position = None
            for pos in positions:
                if pos.stock_code == stock_code and pos.volume > 0:  # 确保只考虑持仓数量大于0的股票
                    current_position = pos
                    break
            
            if not current_position or current_position.volume <= 0:
                # 使用定期日志，避免频繁输出
                self.log_periodic(f"{stock_code}当前无可卖出持仓")
                return
            
            # 检查T+1限制，如果可用数量为0则跳过
            if current_position.can_use_volume <= 0:
                # 使用定期日志，避免频繁输出
                self.log_periodic(f"{stock_code}为T+1产品，当前无可用余额，跳过卖出")
                return
                
            # 计算当前持仓市值
            current_position_value = current_position.volume * latest_price
            # 使用定期日志，避免频繁输出
            self.log_periodic(f"{stock_code}当前持仓: {current_position.volume}股，可用: {current_position.can_use_volume}股，市值: {current_position_value:.2f}元")
            
            # 获取单只总金额和单笔金额（使用缓存的配置）
            total_amount = float(self._get_cached_config('total_amount'))
            single_amount = float(self._get_cached_config('single_amount'))
            
            # 非板块股票，分批卖出而不是一次性全部卖出
            self.log_message(f"{stock_code}不在当前板块中，准备分批卖出")
            
            # 总持仓市值
            total_position_value = current_position.can_use_volume * latest_price
            
            # 使用单笔金额限制每次卖出的金额
            sell_amount = min(single_amount, total_position_value)
            
            # 计算委托数量
            volume = int(sell_amount / latest_price)
            
            # 确保不超过持仓可用数量
            volume = min(volume, current_position.can_use_volume)
            
            # 根据证券类型调整委托数量
            if stock_code.startswith(('11', '12')):  # 可转债
                volume = (volume // 10) * 10
            else:  # 股票和ETF
                volume = (volume // 100) * 100
            
            # 如果卖出数量为0，直接返回，不记录日志
            if volume <= 0:
                return
                
            self.log_message(f"{stock_code}最终卖出数量: {volume}股，预计金额: {volume * latest_price:.2f}元")

            # 设置委托价格和类型
            price_type = self.price_type.get()
            price = latest_price  # 默认使用最新价，避免0价格风险
            adjust_percent = float(self.price_adjust.get()) / 100

            # 详细的价格计算日志
            self.log_message(f"{stock_code} 卖出价格计算:")
            self.log_message(f"   最新价: {latest_price}")
            self.log_message(f"   价格类型: {price_type}")
            self.log_message(f"   价格调整: {adjust_percent*100}%")

            if price_type == "限价":
                price = latest_price * (1 - adjust_percent)  # 卖出时减去价格调整的百分比
                price = self._round_price(price, stock_code)
                self.log_message(f"   限价委托价格: {latest_price} × (1 - {adjust_percent}) = {price}")
            elif price_type == "对手方最优":
                # 对手方最优市价单，使用调整后的价格作为参考价格
                price = latest_price * (1 - adjust_percent)
                price = self._round_price(price, stock_code)
                self.log_message(f"   对手方最优参考价格: {latest_price} × (1 - {adjust_percent}) = {price}")
            elif price_type == "本方最优":
                # 本方最优市价单，使用最新价作为参考价格
                price = latest_price
                self.log_message(f"   本方最优参考价格: {price}")
            elif price_type == "最优五档":
                # 获取五档数据并选择最优价格
                five_level_data = self._get_five_level_data(stock_code)
                if five_level_data:
                    self._log_five_level_info(stock_code, five_level_data)
                    price = self._calculate_five_level_price_with_adjustment(
                        five_level_data, is_buy=False, adjust_percent=adjust_percent)
                    price = self._round_price(price, stock_code)
                    self.log_message(f"   最优五档卖出价格: {price}")
                else:
                    # 获取五档数据失败，降级为对手方最优
                    price = latest_price * (1 - adjust_percent)
                    price = self._round_price(price, stock_code)
                    self.log_message(f"   五档数据获取失败，降级为参考价格: {price}")
            elif price_type == "最优五档转限价":
                # 检查是否为可转债
                if stock_code.startswith(('11', '12')):  # 可转债
                    # 可转债直接使用最新价进行限价委托
                    price = latest_price
                    price_type = "限价"
                    self.log_message(f"   可转债限价委托价格: {price}")
                else:
                    # 获取五档数据
                    five_level_data = self._get_five_level_data(stock_code)
                    if five_level_data:
                        self._log_five_level_info(stock_code, five_level_data)
                        # 尝试五档委托
                        best_price, level = self._select_best_five_level_price(
                            five_level_data, is_buy=False, min_volume=100)

                        if level > 0:
                            # 五档有合适价格，使用五档委托
                            price = best_price
                            self.log_message(f"   五档转限价，使用第{level}档价格: {price}")
                            # 保持原有的五档委托类型
                        else:
                            # 五档无合适价格，转为限价委托
                            price = latest_price * (1 - adjust_percent)
                            price = self._round_price(price, stock_code)
                            price_type = "限价"
                            self.log_message(f"   五档无合适价格，转限价委托: {price}")
                    else:
                        # 获取五档数据失败，直接转限价
                        price = latest_price * (1 - adjust_percent)
                        price = self._round_price(price, stock_code)
                        price_type = "限价"
                        self.log_message(f"   获取五档失败，转限价委托: {price}")

            # 最终价格安全检查，确保价格不为0
            if price <= 0:
                price = latest_price
                self.log_message(f"⚠️ {stock_code}价格异常，使用最新价作为安全价格：{price}")

            # 验证价格是否在涨跌停范围内
            price = self._validate_price_range(price, latest_price, stock_code, is_buy=False)

            self.log_message(f"   最终委托价格: {price}")

            # 处理五档转限价的动态类型转换
            final_price_type = price_type
            # 注意：这里的price_type可能在上面的逻辑中被修改为"限价"

            price_type_map = {
                "限价": xtconstant.FIX_PRICE,
                "最优五档": xtconstant.MARKET_SH_CONVERT_5_CANCEL if stock_code.endswith("SH")
                           else xtconstant.MARKET_SZ_CONVERT_5_CANCEL,
                "最优五档转限价": xtconstant.FIX_PRICE if final_price_type == "限价" else (
                    xtconstant.MARKET_SH_CONVERT_5_CANCEL if stock_code.endswith("SH")
                    else xtconstant.MARKET_SZ_CONVERT_5_CANCEL),
                "对手方最优": xtconstant.MARKET_PEER_PRICE_FIRST,
                "本方最优": xtconstant.MARKET_MINE_PRICE_FIRST
            }

            xt_price_type = price_type_map.get(final_price_type, xtconstant.MARKET_PEER_PRICE_FIRST)
            
            order_id = self.xt_trader.order_stock(
                acc,
                stock_code,
                xtconstant.STOCK_SELL,
                volume,
                xt_price_type,
                price,
                "同花顺板块委托单",
                price_type
            )
            
            # 更新最后交易时间
            self.last_trade_time[stock_code] = current_time

            # 记录当日卖出股票（卖出委托成功后立即记录）
            self._record_sold_stock(stock_code, order_id)

            # 计算预计卖出后的持仓情况
            expected_remaining_volume = current_position.volume - volume
            expected_remaining_value = expected_remaining_volume * latest_price

            self.log_message(f"卖出委托成功: {stock_code}, 数量: {volume}, 价格: {price}, 订单号: {order_id}")
            self.log_message(f"当前持仓: {current_position.volume}股, 市值: {current_position_value:.2f}, " +
                           f"预计卖出后: {expected_remaining_volume}股, 市值: {expected_remaining_value:.2f}")

            # 如果预计卖出后仍有剩余可卖出股票，提示将在交易间隔后继续卖出
            # 由于我们已经在方法开始处过滤掉了板块内股票，这里只需处理非板块股票
            if expected_remaining_volume > 0 and current_position.can_use_volume - volume > 0:
                next_trade_time = current_time + timedelta(seconds=trade_interval)
                self.log_message(f"{stock_code}预计在 {next_trade_time.strftime('%Y-%m-%d %H:%M:%S')} 后继续分批卖出，直到清仓")

        except Exception as e:
            self.log_message(f"卖出委托失败 {stock_code}: {str(e)}")

    def _record_sold_stock(self, stock_code, order_id=None):
        """记录当日卖出的股票"""
        try:
            if self.enable_sold_stock_filter:
                # 检查日期变化
                self._check_trade_date_change()

                # 添加到当日卖出集合
                if stock_code not in self.today_sold_stocks:
                    self.today_sold_stocks.add(stock_code)

                    # 立即保存到文件
                    self._save_daily_trades()

                    self.log_message(f"📝 已记录当日卖出股票: {stock_code}，当日累计卖出 {len(self.today_sold_stocks)} 只")

                    if order_id:
                        self.log_message(f"🔗 卖出订单号: {order_id}")
                else:
                    self.log_message(f"ℹ️ {stock_code} 当日已记录过卖出")

        except Exception as e:
            self.log_message(f"❌ 记录卖出股票失败 {stock_code}: {str(e)}")

    def _is_stock_sold_today(self, stock_code):
        """检查股票是否在当日已卖出"""
        try:
            # 检查日期变化
            self._check_trade_date_change()

            return stock_code in self.today_sold_stocks

        except Exception as e:
            self.log_message(f"❌ 检查当日卖出状态失败 {stock_code}: {str(e)}")
            return False

    def _round_price(self, price, stock_code):
        """根据股票类型进行价格精度处理"""
        try:
            # 🔧 修复：使用Decimal进行精确计算，避免浮点数精度问题
            from decimal import Decimal, ROUND_HALF_UP

            # 将价格转换为Decimal进行精确计算
            decimal_price = Decimal(str(price))

            if stock_code.startswith(('11', '12')):  # 可转债
                # 可转债价格精确到3位小数
                return float(decimal_price.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP))
            elif stock_code.startswith(('68', '30')):  # 科创板、创业板
                # 科创板、创业板价格精确到3位小数
                return float(decimal_price.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP))
            else:  # 主板股票
                # 主板股票价格精确到2位小数
                return float(decimal_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        except Exception as e:
            self.log_message(f"价格精度处理失败 {stock_code}: {e}")
            return round(float(price), 2)  # 默认保留2位小数

    def _calculate_buy_amount_precise(self, stock_code, price, available_money, reserve_money):
        """
        🔧 精确的买入金额计算函数
        使用Decimal避免浮点数精度问题，提供详细的计算过程
        """
        try:
            from decimal import Decimal, ROUND_HALF_UP

            # 使用Decimal进行精确计算
            decimal_price = Decimal(str(price))
            decimal_available = Decimal(str(available_money))
            decimal_reserve = Decimal(str(reserve_money))

            # 计算实际可用资金
            actual_available = decimal_available - decimal_reserve

            # 🔧 修复：只在资金不足时才输出详细日志
            if actual_available <= 0:
                self.log_message(f"💰 {stock_code} 资金不足详情:")
                self.log_message(f"   总可用资金: {float(decimal_available):,.2f}元")
                self.log_message(f"   保留资金: {float(decimal_reserve):,.2f}元")
                self.log_message(f"   实际可用: {float(actual_available):,.2f}元")

            if actual_available <= 0:
                return 0, 0, f"可用资金不足: {float(actual_available):.2f}元"

            # 获取最小交易单位
            min_unit = 100  # 股票默认100股
            if stock_code.startswith(('11', '12')):  # 可转债
                min_unit = 10

            # 计算最小买入金额
            decimal_min_unit = Decimal(str(min_unit))
            min_amount_needed = decimal_price * decimal_min_unit

            if actual_available < min_amount_needed:
                # 只在资金不足时输出详细信息
                self.log_message(f"💰 {stock_code} 资金不足详情:")
                self.log_message(f"   需要: {float(min_amount_needed):,.2f}元")
                self.log_message(f"   可用: {float(actual_available):,.2f}元")
                return 0, 0, f"资金不足买入{min_unit}股，需要{float(min_amount_needed):.2f}元，可用{float(actual_available):.2f}元"

            # 获取单笔金额限制
            single_amount = float(self._get_cached_config('single_amount'))
            decimal_single = Decimal(str(single_amount))

            # 计算本次买入限额
            buy_limit = min(decimal_single, actual_available)

            # 计算可买入数量
            max_units = int(buy_limit / min_amount_needed)
            buy_volume = max_units * min_unit
            buy_amount = float(decimal_price * Decimal(str(buy_volume)))

            # 🔧 修复：移除详细的买入计算日志，保持简洁
            # 只在买入成功后输出简单的确认信息

            return buy_volume, buy_amount, "计算成功"

        except Exception as e:
            self.log_message(f"❌ {stock_code} 买入金额计算失败: {e}")
            return 0, 0, f"计算失败: {e}"

    def _validate_price_range(self, price, latest_price, stock_code, is_buy=True):
        """验证价格是否在合理范围内（涨跌停限制）"""
        try:
            upper_limit = 0
            lower_limit = 0

            # 🔧 优先使用 get_instrument_detail 获取涨跌停价格
            try:
                detail = xtdata.get_instrument_detail(stock_code)
                if detail:
                    upper_limit = detail.get('UpStopPrice', 0)
                    lower_limit = detail.get('DownStopPrice', 0)

                    if upper_limit > 0 and lower_limit > 0:
                        self.log_message(f"📊 {stock_code}从接口获取涨跌停价格: 涨停价={upper_limit}, 跌停价={lower_limit}")
                    else:
                        self.log_message(f"⚠️ {stock_code}接口返回涨跌停价格无效: 涨停价={upper_limit}, 跌停价={lower_limit}")

            except Exception as detail_error:
                self.log_message(f"⚠️ {stock_code}获取涨跌停详情失败: {str(detail_error)}")

            # 🔧 如果接口获取失败，尝试从行情数据获取
            if upper_limit <= 0 or lower_limit <= 0:
                try:
                    market_data = xtdata.get_full_tick([stock_code])
                    if market_data and stock_code in market_data:
                        data = market_data[stock_code]

                        # 尝试多种可能的涨跌停价格字段名
                        upper_limit = (data.get('upperLimit') or
                                     data.get('upLimit') or
                                     data.get('limitUp') or
                                     data.get('涨停价') or
                                     data.get('upper_limit') or
                                     data.get('up_limit') or
                                     data.get('limit_up') or
                                     0)

                        lower_limit = (data.get('lowerLimit') or
                                     data.get('downLimit') or
                                     data.get('limitDown') or
                                     data.get('跌停价') or
                                     data.get('lower_limit') or
                                     data.get('down_limit') or
                                     data.get('limit_down') or
                                     0)

                        if upper_limit > 0 and lower_limit > 0:
                            self.log_message(f"📊 {stock_code}从行情数据获取涨跌停价格: 涨停价={upper_limit}, 跌停价={lower_limit}")

                except Exception as tick_error:
                    self.log_message(f"⚠️ {stock_code}获取行情数据失败: {str(tick_error)}")

            # 🔧 如果仍然无法获取，通过前收盘价计算
            if upper_limit <= 0 or lower_limit <= 0:
                try:
                    # 尝试从行情数据获取前收盘价
                    market_data = xtdata.get_full_tick([stock_code])
                    if market_data and stock_code in market_data:
                        data = market_data[stock_code]
                        last_close = data.get('lastClose', 0)

                        if last_close > 0:
                            # 根据股票类型确定涨跌幅限制
                            limit_percent = self._get_limit_percent(stock_code)
                            upper_limit = round(last_close * (1 + limit_percent), 2)
                            lower_limit = round(last_close * (1 - limit_percent), 2)
                            self.log_message(f"📈 {stock_code}通过前收盘价计算涨跌停: 前收盘={last_close}, 涨停价={upper_limit}, 跌停价={lower_limit}")
                        else:
                            self.log_message(f"⚠️ {stock_code}无法获取前收盘价，跳过涨跌停验证")

                except Exception as calc_error:
                    self.log_message(f"⚠️ {stock_code}计算涨跌停价格失败: {str(calc_error)}")

            # 🔧 执行价格范围验证
            if upper_limit > 0 and lower_limit > 0:
                if is_buy:
                    # 买入价格不能超过涨停价
                    if price > upper_limit:
                        self.log_message(f"⚠️ {stock_code}买入价格{price}超过涨停价{upper_limit}，调整为涨停价")
                        return upper_limit
                else:
                    # 卖出价格不能低于跌停价
                    if price < lower_limit:
                        self.log_message(f"⚠️ {stock_code}卖出价格{price}低于跌停价{lower_limit}，调整为跌停价")
                        return lower_limit

                self.log_message(f"✅ {stock_code}价格{price}在涨跌停范围内({lower_limit}-{upper_limit})")
            else:
                self.log_message(f"⚠️ {stock_code}无法获取有效涨跌停价格，使用原价格{price}")

            return price

        except Exception as e:
            self.log_message(f"❌ {stock_code}价格范围验证失败: {str(e)}")
            return price

    def _get_limit_percent(self, stock_code):
        """根据股票代码获取涨跌幅限制百分比"""
        try:
            # ST股票 5%
            if '*' in stock_code or stock_code.startswith('ST') or 'ST' in stock_code:
                return 0.05
            
            # 主板、中小板 10%
            if stock_code.startswith(('60', '00')) and stock_code.endswith(('.SH', '.SZ')):
                return 0.1
            
            # 创业板 20%
            if stock_code.startswith('30') and stock_code.endswith('.SZ'):
                return 0.2
                
            # 科创板 20%
            if stock_code.startswith('68') and stock_code.endswith('.SH'):
                return 0.2
                
            # 北交所 30%
            if stock_code.startswith(('8', '4')) or stock_code.endswith('.BJ'):
                return 0.3
                
            # 默认 10%
            return 0.1
            
        except Exception as e:
            self.log_message(f"获取涨跌幅限制失败 {stock_code}: {e}")
            return 0.1  # 默认10%

    def get_latest_price(self, stock_code):
        """获取最新价格的函数"""
        try:
            # 🚨 紧急启用详细调试模式，追踪价格异常问题
            debug_mode = True  # 临时强制启用调试模式

            self.log_message(f"开始获取 {stock_code} 行情数据")
            if debug_mode:
                self.log_message(f"{stock_code} 详细行情获取调试:")

            # 🚨 改进：为不同市场股票尝试多种代码格式
            codes_to_try = [stock_code]

            # 北交所股票处理
            if stock_code.endswith('.BJ'):
                base_code = stock_code[:-3]
                codes_to_try.append(base_code)
            elif stock_code.isdigit() and len(stock_code) == 6 and stock_code.startswith(('8', '4', '9')):
                codes_to_try.append(f"{stock_code}.BJ")

            # 🚨 新增：创业板股票处理（30开头）
            elif stock_code.startswith('30') and not stock_code.endswith('.SZ'):
                codes_to_try.append(f"{stock_code}.SZ")
            elif stock_code.endswith('.SZ') and stock_code.startswith('30'):
                base_code = stock_code[:-3]
                codes_to_try.append(base_code)

            # 🚨 新增：主板股票处理
            elif stock_code.startswith('60') and not stock_code.endswith('.SH'):
                codes_to_try.append(f"{stock_code}.SH")
            elif stock_code.startswith('00') and not stock_code.endswith('.SZ'):
                codes_to_try.append(f"{stock_code}.SZ")

            if debug_mode:
                self.log_message(f"   准备尝试的代码格式: {codes_to_try}")

            # 尝试不同的代码格式获取行情
            for code in codes_to_try:
                try:
                    if debug_mode:
                        self.log_message(f"   尝试获取代码: {code}")

                    # 使用xtdata获取实时行情
                    market_data = xtdata.get_full_tick([code])

                    if market_data and code in market_data:
                        data = market_data[code]
                        last_price = data['lastPrice']

                        if debug_mode:
                            # 🔧 获取股票名称和状态信息
                            stock_name = "未知股票"
                            upper_limit = 0
                            lower_limit = 0

                            # 获取股票名称和涨跌停价格
                            try:
                                detail = xtdata.get_instrument_detail(code)
                                if detail:
                                    stock_name = detail.get('InstrumentName', '未知股票')
                                    upper_limit = detail.get('UpStopPrice', 0)
                                    lower_limit = detail.get('DownStopPrice', 0)
                            except Exception:
                                pass

                            # 获取五档行情信息
                            ask_price = data.get('askPrice', [0])[0] if data.get('askPrice') else 0
                            bid_price = data.get('bidPrice', [0])[0] if data.get('bidPrice') else 0

                            # 🎯 生成统一格式的日志信息
                            status_info = self._generate_stock_status_info(
                                code, stock_name, last_price, upper_limit, lower_limit,
                                ask_price, bid_price
                            )

                            self.log_message(status_info)

                        if last_price > 0:
                            # 🚨 紧急添加：价格合理性验证
                            price_valid, price_error = self._validate_price_reasonableness(stock_code, last_price, code)
                            if not price_valid:
                                self.log_message(f"🚨 价格异常警告 {stock_code}: {price_error}")
                                self.log_message(f"🚨 获取价格: {last_price}元，使用代码: {code}")
                                self.log_message(f"🚨 为安全起见，跳过此股票的交易")
                                continue  # 尝试下一个代码格式或跳过

                            # 🔧 修复：对价格进行精度处理，避免浮点数精度问题
                            precise_price = self._round_price(last_price, stock_code)

                            # 成功获取到有效价格，记录使用的代码格式
                            if code != stock_code:
                                self.log_message(f"使用代码格式 {code} 成功获取 {stock_code} 行情数据")

                            # 如果价格被精度处理修正，记录日志
                            if abs(precise_price - last_price) > 0.0001:
                                self.log_message(f"🔧 {stock_code} 价格精度修正: {last_price} → {precise_price}")

                            return precise_price
                        else:
                            if debug_mode:
                                self.log_message(f"❌ {code}最新价格无效: {last_price}")
                            else:
                                self.log_message(f"{code}最新价格无效: {last_price}")

                except Exception as e:
                    if debug_mode:
                        self.log_message(f"❌ 获取{code}行情失败: {e}")
                    # 单个格式失败，继续尝试下一个
                    continue

            # 所有格式都失败
            if debug_mode:
                self.log_message(f"❌ {stock_code} 行情获取完全失败，已尝试格式: {codes_to_try}")
            else:
                self.log_message(f"获取{stock_code}行情数据失败，已尝试格式: {codes_to_try}")
            return 0

        except Exception as e:
            self.log_message(f"获取{stock_code}最新价格失败: {str(e)}")
            return 0

    def _generate_stock_status_info(self, stock_code, stock_name, current_price, upper_limit, lower_limit, ask_price, bid_price):
        """
        生成统一格式的股票状态信息
        格式：股票代码(股票名称): 价格 + 状态描述 + 流动性 + 委托价格
        """
        try:
            # 提取股票代码（去掉后缀）
            base_code = stock_code.split('.')[0] if '.' in stock_code else stock_code

            # 判断股票状态
            status_desc = ""
            liquidity_desc = ""
            order_price = current_price

            # 判断涨停/跌停状态
            if upper_limit > 0 and lower_limit > 0:
                if abs(current_price - upper_limit) < 0.01:  # 涨停
                    status_desc = "涨停封板"
                    # 计算委托价格（涨停价）
                    order_price = upper_limit
                elif abs(current_price - lower_limit) < 0.01:  # 跌停
                    status_desc = "跌停封板"
                    order_price = lower_limit
                elif current_price >= upper_limit * 0.98:  # 接近涨停
                    status_desc = "接近涨停"
                    order_price = upper_limit
                elif current_price <= lower_limit * 1.02:  # 接近跌停
                    status_desc = "接近跌停"
                    order_price = lower_limit
                else:
                    status_desc = f"{current_price}元"
                    # 根据价格类型和调整计算委托价格
                    try:
                        price_adjust_percent = float(self.price_adjust.get()) / 100
                    except:
                        price_adjust_percent = 0.5 / 100  # 默认0.5%
                    order_price = round(current_price * (1 + price_adjust_percent), 2)
                    # 确保不超过涨停价
                    if order_price > upper_limit:
                        order_price = upper_limit
            else:
                status_desc = f"{current_price}元"
                # 没有涨跌停信息时的默认处理
                try:
                    price_adjust_percent = float(self.price_adjust.get()) / 100
                except:
                    price_adjust_percent = 0.5 / 100  # 默认0.5%
                order_price = round(current_price * (1 + price_adjust_percent), 2)

            # 判断流动性状态
            if ask_price <= 0:  # 无卖盘
                if status_desc.endswith("涨停封板"):
                    liquidity_desc = "无卖盘"
                else:
                    liquidity_desc = "无卖盘"
            elif bid_price <= 0:  # 无买盘
                if status_desc.endswith("跌停封板"):
                    liquidity_desc = "无买盘"
                else:
                    liquidity_desc = "无买盘"
            else:
                liquidity_desc = "有卖盘"

            # 生成最终的状态信息（使用格式化价格）
            formatted_current_price = self._format_price(current_price)
            formatted_order_price = self._format_price(order_price)

            if status_desc.endswith("元"):
                # 正常交易状态
                status_info = f"{base_code}({stock_name}): {status_desc} {liquidity_desc} 委托价{formatted_order_price}"
            else:
                # 涨停/跌停状态
                status_info = f"{base_code}({stock_name}): {formatted_current_price}元{status_desc} {liquidity_desc} 委托价{formatted_order_price}"

            return status_info

        except Exception:
            # 异常情况下返回基础信息
            base_code = stock_code.split('.')[0] if '.' in stock_code else stock_code
            formatted_current_price = self._format_price(current_price)
            return f"{base_code}({stock_name}): {formatted_current_price}元 状态未知 委托价{formatted_current_price}"

    def _validate_price_reasonableness(self, stock_code, price, used_code):
        """
        🚨 紧急价格合理性验证机制
        防止异常价格导致的交易损失
        支持多种市场类型的价格上限配置
        """
        try:
            # 基本价格验证
            if price <= 0:
                return False, f"价格不能为0或负数: {price}"

            # 获取价格限制配置
            price_limits = {
                'default': 3000,    # 默认价格上限
                'chuangye': 500,    # 创业板价格上限
                'kechuang': 1000,   # 科创板价格上限
                'zhuban': 1000,     # 主板价格上限
                'beijiao': 200,     # 北交所价格上限
                'st': 50,           # ST股票价格上限
                'index': 5000,      # 指数价格上限
                'hk': 1000,         # 港股价格上限
                'us': 10000,        # 美股价格上限
                'futures': 100000   # 期货价格上限
            }
            
            # 从配置文件读取价格限制（如果存在）
            try:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        if 'price_limits' in config:
                            price_limits.update(config['price_limits'])
            except Exception:
                # 如果读取配置失败，使用默认值
                pass

            # 检查是否为ST股票（名称包含ST）
            is_st = '*' in stock_code or stock_code.startswith('ST') or 'ST' in stock_code
            
            # 根据股票代码判断市场类型
            base_code = stock_code.split('.')[0] if '.' in stock_code else stock_code
            used_base_code = used_code.split('.')[0] if '.' in used_code else used_code
            
            # 判断股票类型并设置价格上限
            price_limit = price_limits['default']
            stock_type = "默认"
            
            # ST股票检查
            if is_st:
                price_limit = price_limits['st']
                stock_type = "ST股票"
            
            # 创业板股票检查（30开头的股票）
            elif base_code.startswith('30') or used_base_code.startswith('30'):
                price_limit = price_limits['chuangye']
                stock_type = "创业板"
            
            # 科创板股票检查（688开头的股票）
            elif base_code.startswith('688') or used_base_code.startswith('688'):
                price_limit = price_limits['kechuang']
                stock_type = "科创板"
            
            # 主板股票检查（60开头、00开头）
            elif base_code.startswith(('60', '00')) or used_base_code.startswith(('60', '00')):
                price_limit = price_limits['zhuban']
                stock_type = "主板"
            
            # 北交所股票检查（8、4、9开头或.BJ后缀）
            elif (stock_code.startswith(('8', '4', '9')) or stock_code.endswith('.BJ') or
                  used_code.startswith(('8', '4', '9')) or used_code.endswith('.BJ')):
                price_limit = price_limits['beijiao']
                stock_type = "北交所"
            
            # 港股检查（.HK后缀）
            elif stock_code.endswith('.HK') or used_code.endswith('.HK'):
                price_limit = price_limits['hk']
                stock_type = "港股"
            
            # 美股检查（.US后缀）
            elif stock_code.endswith('.US') or used_code.endswith('.US'):
                price_limit = price_limits['us']
                stock_type = "美股"
            
            # 期货检查（.FT后缀）
            elif stock_code.endswith('.FT') or used_code.endswith('.FT'):
                price_limit = price_limits['futures']
                stock_type = "期货"
            
            # 指数检查（.IDX后缀）
            elif stock_code.endswith('.IDX') or used_code.endswith('.IDX'):
                price_limit = price_limits['index']
                stock_type = "指数"

            # 价格上限检查
            if price > price_limit:
                return False, f"{stock_type}价格{price}元超过设定上限{price_limit}元，可能获取了错误的数据"

            # 代码格式一致性检查
            if stock_code != used_code:
                # 检查是否是合理的代码转换
                base_code_check = stock_code.split('.')[0] if '.' in stock_code else stock_code
                used_base_code_check = used_code.split('.')[0] if '.' in used_code else used_code
                if base_code_check != used_base_code_check:
                    return False, f"使用代码{used_code}获取{stock_code}价格，代码不匹配"

            return True, ""

        except Exception as e:
            return False, f"价格验证过程出错: {e}"

    def run(self):
        self.root.mainloop()

    def clear_stock_pool(self, pool_type):
        """清空板块功能已不适用于同花顺板块，提示用户使用同花顺软件修改板块"""
        # pool_type 参数保留以兼容接口，但在同花顺模式下不使用
        messagebox.showinfo("提示", "同花顺板块无法通过此软件直接清空，请使用同花顺软件修改板块内容")
        self.log_message("同花顺板块需要在同花顺软件中修改")

    def clear_log(self):
        """清空所有日志（改进版）"""
        try:
            # 记录清空操作时间
            clear_time = datetime.now().strftime("%H:%M:%S")
            clear_message = f"[{clear_time}] 日志清空操作执行"
            detailed_clear_message = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [INFO] 日志清空操作执行"

            # 清空所有日志显示
            if hasattr(self, 'simplified_log_text') and self.simplified_log_text:
                self.simplified_log_text.delete(1.0, tk.END)
                # 在简化日志中显示清空确认
                self.simplified_log_text.insert(tk.END, f"{clear_message}\n")

            if hasattr(self, 'detailed_log_text') and self.detailed_log_text:
                self.detailed_log_text.delete(1.0, tk.END)
                # 在详细日志中显示清空确认
                self.detailed_log_text.insert(tk.END, f"{detailed_clear_message}\n")

            # 清空增强日志管理器（如果可用）
            if hasattr(self, 'enhanced_log_manager') and self.enhanced_log_manager:
                try:
                    self.enhanced_log_manager.clear_all_logs()
                except AttributeError:
                    # 如果没有clear_all_logs方法，尝试其他清空方法
                    if hasattr(self.enhanced_log_manager, 'clear_simplified_logs'):
                        self.enhanced_log_manager.clear_simplified_logs()
                    if hasattr(self.enhanced_log_manager, 'clear_detailed_logs'):
                        self.enhanced_log_manager.clear_detailed_logs()

        except Exception as e:
            messagebox.showerror("错误", f"清空日志失败: {str(e)}")

    def validate_trade_interval(self):
        """验证交易间隔输入是否有效"""
        try:
            interval_str = self.trade_interval_entry.get().strip()
            if not interval_str:
                return False, "交易间隔不能为空"

            interval = float(interval_str)
            if interval <= 0:
                return False, "交易间隔必须大于0"

            if interval < 0.1:
                return False, "交易间隔不能小于0.1秒"

            if interval > 3600:
                return False, "交易间隔不能超过3600秒"

            return True, ""
        except ValueError:
            return False, "交易间隔必须是有效的数字"

    def validate_stock_code_format(self, stock_code):
        """验证股票代码格式是否正确"""
        import re

        # 定义各种股票代码格式的正则表达式
        patterns = [
            r'^\d{6}$',                    # 6位数字（如000001）
            r'^\d{6}\.(SH|SZ|BJ|NQ)$',     # 6位数字+交易所后缀
            r'^[A-Z]{1,5}\.US$',           # 美股代码
            r'^\d{5}\.HK$',                # 港股代码
            r'^[A-Z]{1,10}\d{4}\.FT$',     # 期货代码
        ]

        stock_code = stock_code.upper().strip()

        for pattern in patterns:
            if re.match(pattern, stock_code):
                return True

        return False

    def get_stock_market_type(self, stock_code):
        """获取股票的市场类型"""
        stock_code = stock_code.upper().strip()

        # 北交所
        if (stock_code.isdigit() and len(stock_code) == 6 and stock_code.startswith(('8', '4', '9'))) or stock_code.endswith('.BJ'):
            return "北交所"

        # 科创板
        if stock_code.startswith('688') or (stock_code.endswith('.SH') and stock_code.startswith('688')):
            return "科创板"

        # 创业板
        if stock_code.startswith('300') or (stock_code.endswith('.SZ') and stock_code.startswith('300')):
            return "创业板"

        # 新三板
        if stock_code.endswith('.NQ'):
            return "新三板"

        # 美股
        if stock_code.endswith('.US'):
            return "美股"

        # 港股
        if stock_code.endswith('.HK'):
            return "港股"

        # 期货
        if stock_code.endswith('.FT'):
            return "期货"

        # 沪市
        if stock_code.endswith('.SH') or (stock_code.isdigit() and stock_code.startswith(('6', '0'))):
            return "沪市"

        # 深市
        if stock_code.endswith('.SZ') or (stock_code.isdigit() and stock_code.startswith(('0', '2'))):
            return "深市"

        return "未知"

    def _get_five_level_data(self, stock_code):
        """获取并验证五档行情数据"""
        try:
            market_data = xtdata.get_full_tick([stock_code])
            if not market_data or stock_code not in market_data:
                self.log_message(f"获取{stock_code}五档数据失败")
                return None

            data = market_data[stock_code]

            # 验证数据完整性
            ask_prices = data.get('askPrice', [])
            bid_prices = data.get('bidPrice', [])
            ask_volumes = data.get('askVolume', [])
            bid_volumes = data.get('bidVolume', [])

            if len(ask_prices) < 5 or len(bid_prices) < 5:
                self.log_message(f"{stock_code}五档数据不完整")
                return None

            return {
                'ask_prices': ask_prices[:5],
                'bid_prices': bid_prices[:5],
                'ask_volumes': ask_volumes[:5] if ask_volumes else [0]*5,
                'bid_volumes': bid_volumes[:5] if bid_volumes else [0]*5,
                'last_price': data.get('lastPrice', 0)
            }
        except Exception as e:
            self.log_message(f"获取{stock_code}五档数据异常: {str(e)}")
            return None

    def _select_best_five_level_price(self, five_level_data, is_buy=True, min_volume=100):
        """从五档数据中选择最优价格"""
        try:
            if is_buy:
                # 买入：选择卖盘中价格最低且成交量充足的档位
                prices = five_level_data['ask_prices']
                volumes = five_level_data['ask_volumes']
            else:
                # 卖出：选择买盘中价格最高且成交量充足的档位
                prices = five_level_data['bid_prices']
                volumes = five_level_data['bid_volumes']

            # 遍历五档，选择最优价格
            for i in range(5):
                price = prices[i]
                volume = volumes[i] if i < len(volumes) else 0

                if price > 0 and volume >= min_volume:
                    self.log_message(f"选择第{i+1}档价格: {price}, 成交量: {volume}")
                    return price, i+1

            # 如果五档都不满足条件，返回最新价
            last_price = five_level_data['last_price']
            self.log_message(f"五档价格都不满足条件，使用最新价: {last_price}")
            return last_price, 0

        except Exception as e:
            self.log_message(f"五档价格选择异常: {str(e)}")
            return five_level_data['last_price'], 0

    def _calculate_five_level_price_with_adjustment(self, five_level_data, is_buy=True, adjust_percent=0):
        """计算带价格调整的五档最优价格"""
        try:
            best_price, level = self._select_best_five_level_price(five_level_data, is_buy)

            if level > 0:  # 成功选择了五档价格
                if is_buy:
                    # 买入时，在最优价格基础上适当上调，提高成交概率
                    adjusted_price = best_price * (1 + adjust_percent)
                else:
                    # 卖出时，在最优价格基础上适当下调，提高成交概率
                    adjusted_price = best_price * (1 - adjust_percent)

                self.log_message(f"五档最优价格: {best_price}, 调整后价格: {adjusted_price}")
                return adjusted_price
            else:
                # 未能选择五档价格，使用最新价加调整
                last_price = five_level_data['last_price']
                if is_buy:
                    adjusted_price = last_price * (1 + adjust_percent)
                else:
                    adjusted_price = last_price * (1 - adjust_percent)

                self.log_message(f"使用最新价调整: {last_price} -> {adjusted_price}")
                return adjusted_price

        except Exception as e:
            self.log_message(f"五档价格计算异常: {str(e)}")
            return five_level_data['last_price']

    def _log_five_level_info(self, stock_code, five_level_data):
        """记录五档详细信息"""
        try:
            self.log_message(f"📊 {stock_code} 五档行情:")
            self.log_message(f"   卖五: {five_level_data['ask_prices'][4]:.3f} ({five_level_data['ask_volumes'][4]})")
            self.log_message(f"   卖四: {five_level_data['ask_prices'][3]:.3f} ({five_level_data['ask_volumes'][3]})")
            self.log_message(f"   卖三: {five_level_data['ask_prices'][2]:.3f} ({five_level_data['ask_volumes'][2]})")
            self.log_message(f"   卖二: {five_level_data['ask_prices'][1]:.3f} ({five_level_data['ask_volumes'][1]})")
            self.log_message(f"   卖一: {five_level_data['ask_prices'][0]:.3f} ({five_level_data['ask_volumes'][0]})")
            self.log_message(f"   最新: {five_level_data['last_price']:.3f}")
            self.log_message(f"   买一: {five_level_data['bid_prices'][0]:.3f} ({five_level_data['bid_volumes'][0]})")
            self.log_message(f"   买二: {five_level_data['bid_prices'][1]:.3f} ({five_level_data['bid_volumes'][1]})")
            self.log_message(f"   买三: {five_level_data['bid_prices'][2]:.3f} ({five_level_data['bid_volumes'][2]})")
            self.log_message(f"   买四: {five_level_data['bid_prices'][3]:.3f} ({five_level_data['bid_volumes'][3]})")
            self.log_message(f"   买五: {five_level_data['bid_prices'][4]:.3f} ({five_level_data['bid_volumes'][4]})")
        except Exception as e:
            self.log_message(f"记录五档信息失败: {str(e)}")

    def on_entry_click(self, entry, default_text):
        """当输入框得焦点时，如果内是默认提示文本则清空"""
        if entry.get() == default_text:
            entry.delete(0, tk.END)
            entry.config(foreground='black')  # 将文本颜色改为黑色

    def on_focus_out(self, entry, default_text):
        """当输入框失去焦点时，如果内容为空则显示默认提示文本"""
        if entry.get() == '':
            entry.insert(0, default_text)
            entry.config(foreground='gray')  # 将提示文本颜色设为灰色
        # 调用保存方法，确保所有输入框失去焦点时都能保存配置
        self.on_focus_out_save()

    def on_focus_out_save(self):
        """当输入框失去焦点时静默自动保存配置"""
        import time

        # 防抖机制：避免频繁保存
        current_time = time.time()
        if current_time - self.last_silent_save_time < self.silent_save_interval:
            return  # 距离上次保存时间太短，跳过本次保存

        try:
            self.save_config_silent()  # 使用静默保存方法
            self.last_silent_save_time = current_time  # 更新最后保存时间
        except Exception as e:
            logger.error(f"保存当日交易记录失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.log_message(f"保存当日交易记录失败: {str(e)}")
            
    def backup_trade_records(self):
        try:
            self.save_trade_records(self.backup_trade_records_path)
        except Exception as e:
            logger.error(f"备份当日交易记录失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.log_message(f"备份当日交易记录失败: {str(e)}")
            
    def is_trading_time(self, current_time=None):
        """
        检查当前是否为交易时间（线程安全版本）

        参数:
            current_time: 当前时间，格式为"HH:MM:SS"，如果为None则使用当前时间

        返回:
            bool: 如果是交易时间返回True，否则返回False
        """
        try:
            if current_time is None:
                current_time = datetime.now().strftime("%H:%M:%S")

            # 使用缓存的时间设置，避免在多线程中访问GUI组件
            start_time, end_time = self._get_cached_trading_times()

            # 使用datetime对象进行比较，更加准确
            try:
                current_dt = datetime.strptime(current_time, "%H:%M:%S").time()
                start_dt = datetime.strptime(start_time, "%H:%M:%S").time()
                end_dt = datetime.strptime(end_time, "%H:%M:%S").time()

                return start_dt <= current_dt <= end_dt

            except ValueError:
                # 如果时间格式解析失败，回退到字符串比较
                self.log_message("时间格式解析失败，使用字符串比较")
                return start_time <= current_time <= end_time

        except Exception as e:
            self.log_message(f"交易时间检查失败: {str(e)}")
            # 出错时默认允许交易（避免影响正常交易）
            return True

    def silent_save_config(self):
        try:
            self.save_config(self.config_file)
        except Exception as e:
            # 静默保存失败时只记录日志，不显示弹窗
            self.log_message(f"静默自动保存配置失败: {str(e)}")
            
    def set_entry_value(self, entry, value, default_text=None):
        """设置输入框的值，并根据是否是默认文本设置适当的颜色"""
        if value:
            entry.delete(0, tk.END)
            entry.insert(0, value)
            entry.config(foreground='black')
        elif default_text:
            entry.delete(0, tk.END)
            entry.insert(0, default_text)
            entry.config(foreground='gray')

    def get_entry_value(self, entry, default_text):
        """获取输入框的实际值，如果是默认提示文本则返回空字符串"""
        value = entry.get()
        return '' if value == default_text else value

    def refresh_block_info(self):
        """手动刷新板块信息"""
        try:
            ths_path = self.get_entry_value(self.ths_path_entry, "示例：D:\\RJ\\10jqka\\同花顺\\mx_148114686")
            block_name = self.get_entry_value(self.block_name_entry, "输入板块名称")
            
            if not ths_path or not block_name:
                messagebox.showerror("错误", "同花顺目录或板块名称不能为空")
                return
                
            # 验证同花顺目录是否存在
            if not os.path.exists(ths_path):
                messagebox.showerror("错误", "同花顺目录不存在")
                return
                
            # 验证StockBlock.ini文件是否存在
            stock_block_file = os.path.join(ths_path, "StockBlock.ini")
            if not os.path.exists(stock_block_file):
                messagebox.showerror("错误", "同花顺板块文件(StockBlock.ini)不存在，请确认目录是否正确")
                return
                
            # 验证板块是否存在
            block_name = self.get_entry_value(self.block_name_entry, "输入板块名称")
            stocks = get_stocks_by_block_name(block_name, ths_path)
            if not stocks:
                self.log_message(f"警告: 板块 {block_name} 中没有股票")
                if not messagebox.askyesno("警告", f"板块 {block_name} 中没有股票，是否继续?"):
                    return
            
            # 显示板块信息
            stock_info = []
            for stock in list(stocks)[:20]:  # 只显示前20只股票
                stock_info.append(stock)
            
            stock_display = ", ".join(stock_info)
            if len(stocks) > 20:
                stock_display += f"... 等共{len(stocks)}只股票"
            
            self.log_message(f"板块 {block_name} 包含 {len(stocks)} 只股票: {stock_display}")
            messagebox.showinfo("板块信息", f"板块 {block_name} 包含 {len(stocks)} 只股票\n\n{stock_display}")
            
            # 重置板块股票的交易时间记录
            for stock in stocks:
                if stock in self.last_trade_time:
                    del self.last_trade_time[stock]
            self.log_message("已重置板块股票的交易时间记录")
            
            # 重置板块缓存，强制下次监控时重新读取
            self.last_block_stocks = set()
            
        except Exception as e:
            error_msg = f"刷新板块信息失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def _create_new_trades_record(self):
        """创建新的当日交易记录"""
        logger.info("开始创建新的当日交易记录...")
        try:
            # 创建新的空记录
            new_record = {
                "trade_date": self.current_trade_date.strftime("%Y-%m-%d"),
                "sold_stocks": []
            }
            
            # 保存到文件
            with open(self.daily_trades_file, 'w', encoding='utf-8') as f:
                json.dump(new_record, f, ensure_ascii=False, indent=2)
            
            logger.info("已创建新的当日交易记录")
            self.log_message("已创建新的当日交易记录")
            
        except Exception as e:
            logger.error(f"创建新交易记录失败: {str(e)}")
            logger.error(traceback.format_exc())
            self.log_message(f"创建新交易记录失败: {str(e)}")

if __name__ == "__main__":
    import logging
    import traceback
    
    # 配置日志记录器
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 启动同花顺交易程序...")
    logger.info(f"📍 当前工作目录: {os.getcwd()}")

    try:
        logger.info("🔧 创建应用实例...")
        app = TongHuaShunTrader()
        logger.info("ℹ️ 程序已启动，GUI界面正在运行...")
        app.root.mainloop()

    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断程序")
    except Exception as e:
        logger.error(f"❌ 程序运行出错: {str(e)}")
        logger.error(traceback.format_exc())
    finally:
        logger.info("👋 程序已退出")