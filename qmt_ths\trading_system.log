2025-08-03 23:11:06,793 - __main__ - INFO - 🚀 启动同花顺交易程序...
2025-08-03 23:11:06,794 - __main__ - INFO - 📍 当前工作目录: C:\Users\<USER>\Desktop\a原版下载无修改168元购买--非加密 - QMT与同花顺结合\qmt_ths
2025-08-03 23:11:06,794 - __main__ - INFO - 🔧 创建应用实例...
2025-08-03 23:11:06,903 - __main__ - INFO - 🔄 初始化当日交易跟踪系统...
2025-08-03 23:11:06,903 - __main__ - INFO - 📂 开始加载当日交易数据...
2025-08-03 23:11:06,903 - __main__ - INFO - 📝 交易记录文件不存在，创建新记录
2025-08-03 23:11:06,904 - __main__ - INFO - 🔧 开始创建新的当日交易记录...
2025-08-03 23:11:06,904 - __main__ - INFO - 💾 开始保存交易记录...
2025-08-03 23:11:06,904 - __main__ - INFO - 🔄 开始创建备份...
2025-08-03 23:11:06,905 - __main__ - INFO - 🔄 备份创建完成
2025-08-03 23:11:06,905 - __main__ - INFO - 📝 开始准备数据...
2025-08-03 23:11:06,905 - __main__ - INFO - 💾 开始写入文件...
2025-08-03 23:11:06,906 - __main__ - INFO - 🔄 开始重命名文件...
2025-08-03 23:11:06,906 - __main__ - INFO - 💾 当日交易记录已保存: 0 只股票
2025-08-03 23:11:06,907 - __main__ - INFO - 📝 已创建新的当日交易记录
2025-08-03 23:11:06,907 - __main__ - INFO - 📂 当日交易数据加载完成
2025-08-03 23:11:06,907 - __main__ - INFO - 📅 开始检查日期变化...
2025-08-03 23:11:06,907 - __main__ - INFO - 📅 日期变化检查完成
2025-08-03 23:11:06,908 - __main__ - INFO - 当日交易跟踪系统初始化完成，已跟踪 0 只卖出股票
2025-08-03 23:11:06,908 - __main__ - INFO - 程序初始化完成，GUI界面已启动
2025-08-03 23:11:06,908 - __main__ - INFO - ℹ️ 程序已启动，GUI界面正在运行...
2025-08-03 23:46:16,786 - __main__ - INFO - 👋 程序已退出
[2025-08-05 21:51:24] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908178, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 21:51:24] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908176, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 21:51:24] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908177, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 21:51:24] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908179, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 21:51:24] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908195, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 21:51:25] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908196, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908178, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908176, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908177, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908179, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908195, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-05 22:07:14] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908196, '错误代码': 251013, '错误信息': '[COUNTER] [251013][不能重复撤单]\r\n[char_config_4101=0]\n\r\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908178, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908176, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908177, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908179, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908195, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
[2025-08-06 00:06:21] [DEBUG] 撤单失败详细信息: {'订单编号': 1098908196, '错误代码': 120141, '错误信息': '[COUNTER] [120141][证券交易未初始化]\r\n[init_date=20250805,curr_date=20250806]\n'}
