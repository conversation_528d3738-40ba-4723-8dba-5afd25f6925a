#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全项目深度搜索和替换
搜索并替换所有相关的作者信息
"""

import os
import re
import shutil
from datetime import datetime

def comprehensive_search():
    """全面搜索所有相关关键词"""
    print("全项目深度搜索")
    print("=" * 60)
    
    # 搜索关键词列表
    search_keywords = [
        "余汉波",
        "yuhanbo", 
        "yhb",
        "YuHanBo",
        "YHB",
        "YUHANBO"
    ]
    
    # 替换映射
    replace_mapping = {
        "余汉波": "risen85",
        "yuhanbo": "risen85",
        "yhb": "risen85", 
        "YuHanBo": "risen85",
        "YHB": "risen85",
        "YUHANBO": "risen85"
    }
    
    # 特殊替换规则
    special_replacements = {
        "余汉波同花顺板块自动交易（微信公众号：余汉波）": "同花顺 QMT 板块全自动交易"
    }
    
    search_results = {}
    
    # 定义要搜索的文件类型
    file_extensions = ['.py', '.json', '.txt', '.md', '.cfg', '.ini', '.xml', '.html', '.js', '.css', '.yml', '.yaml']
    
    print("搜索关键词:", search_keywords)
    print("替换映射:", replace_mapping)
    print("特殊替换:", special_replacements)
    print()
    
    # 遍历项目目录
    for root, dirs, files in os.walk('.'):
        # 跳过一些不需要搜索的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__' and d != 'node_modules']
        
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            # 只搜索指定类型的文件
            if file_ext in file_extensions or file_ext == '':
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        lines = content.split('\n')
                    
                    file_matches = []
                    
                    # 搜索每个关键词
                    for keyword in search_keywords:
                        for line_num, line in enumerate(lines, 1):
                            if keyword in line:
                                file_matches.append({
                                    'keyword': keyword,
                                    'line_number': line_num,
                                    'line_content': line.strip(),
                                    'original_line': line
                                })
                    
                    # 搜索特殊替换规则
                    for special_text in special_replacements.keys():
                        for line_num, line in enumerate(lines, 1):
                            if special_text in line:
                                file_matches.append({
                                    'keyword': 'SPECIAL',
                                    'special_text': special_text,
                                    'line_number': line_num,
                                    'line_content': line.strip(),
                                    'original_line': line
                                })
                    
                    if file_matches:
                        search_results[file_path] = {
                            'matches': file_matches,
                            'content': content,
                            'lines': lines
                        }
                        
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    return search_results, replace_mapping, special_replacements

def generate_replacement_plan(search_results, replace_mapping, special_replacements):
    """生成替换计划"""
    print(f"\n📋 替换计划生成")
    print("=" * 60)
    
    if not search_results:
        print("❌ 未找到需要替换的内容")
        return []
    
    replacement_plan = []
    
    for file_path, file_data in search_results.items():
        file_plan = {
            'file_path': file_path,
            'original_content': file_data['content'],
            'new_content': file_data['content'],
            'changes': []
        }
        
        new_content = file_data['content']
        
        # 应用特殊替换规则
        for special_text, replacement in special_replacements.items():
            if special_text in new_content:
                new_content = new_content.replace(special_text, replacement)
                file_plan['changes'].append({
                    'type': 'special',
                    'from': special_text,
                    'to': replacement
                })
        
        # 应用普通替换规则
        for keyword, replacement in replace_mapping.items():
            if keyword in new_content:
                new_content = new_content.replace(keyword, replacement)
                file_plan['changes'].append({
                    'type': 'normal',
                    'from': keyword,
                    'to': replacement
                })
        
        file_plan['new_content'] = new_content
        
        if file_plan['changes']:
            replacement_plan.append(file_plan)
    
    return replacement_plan

def preview_changes(replacement_plan):
    """预览更改"""
    print(f"\n👀 更改预览")
    print("=" * 60)
    
    if not replacement_plan:
        print("❌ 没有需要更改的内容")
        return
    
    total_files = len(replacement_plan)
    total_changes = sum(len(plan['changes']) for plan in replacement_plan)
    
    print(f"📊 统计信息:")
    print(f"  📁 需要修改的文件: {total_files}")
    print(f"  🔄 总替换次数: {total_changes}")
    print()
    
    for i, plan in enumerate(replacement_plan, 1):
        print(f"📁 文件 {i}: {plan['file_path']}")
        print(f"   🔄 更改数量: {len(plan['changes'])}")
        
        for change in plan['changes']:
            print(f"   {change['type'].upper()}: '{change['from']}' → '{change['to']}'")
        print()

def execute_replacements(replacement_plan, create_backup=True):
    """执行替换操作"""
    print(f"\n🚀 执行替换操作")
    print("=" * 60)
    
    if not replacement_plan:
        print("❌ 没有需要执行的替换")
        return False
    
    success_count = 0
    error_count = 0
    
    # 创建备份目录
    if create_backup:
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        print(f"📦 创建备份目录: {backup_dir}")
    
    for plan in replacement_plan:
        try:
            file_path = plan['file_path']
            
            # 创建备份
            if create_backup:
                backup_path = os.path.join(backup_dir, file_path.replace('./', '').replace('\\', '_').replace('/', '_'))
                os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                shutil.copy2(file_path, backup_path)
            
            # 写入新内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(plan['new_content'])
            
            print(f"✅ 成功更新: {file_path}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 更新失败: {file_path} - {e}")
            error_count += 1
    
    print(f"\n📊 执行结果:")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {error_count} 个文件")
    
    if create_backup:
        print(f"  📦 备份位置: {backup_dir}")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚨 全项目深度搜索和替换")
    print("=" * 80)
    print("搜索并替换所有相关的作者信息")
    print("=" * 80)
    
    # 执行全面搜索
    search_results, replace_mapping, special_replacements = comprehensive_search()
    
    # 生成替换计划
    replacement_plan = generate_replacement_plan(search_results, replace_mapping, special_replacements)
    
    # 预览更改
    preview_changes(replacement_plan)
    
    # 询问是否执行
    if replacement_plan:
        print(f"\n❓ 是否执行替换操作？")
        print("   输入 'yes' 确认执行")
        print("   输入其他任何内容取消")
        
        # 自动执行（在脚本中）
        print("🤖 自动执行替换...")
        success = execute_replacements(replacement_plan, create_backup=True)
        
        if success:
            print(f"\n🎉 替换操作完成！")
            print("✅ 所有相关的作者信息已更新")
            print("📦 原文件已备份")
        else:
            print(f"\n❌ 替换操作失败")
    
    print(f"\n{'='*80}")

if __name__ == "__main__":
    main()
