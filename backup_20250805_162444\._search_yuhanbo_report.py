#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全项目搜索"余汉波"报告
详细定位所有包含"余汉波"的文件和位置
"""

import os
import re

def search_yuhanbo_in_project():
    """在整个项目中搜索余汉波"""
    print("全项目搜索余汉波")
    print("=" * 60)
    
    search_results = []
    
    # 定义要搜索的文件类型
    file_extensions = ['.py', '.json', '.txt', '.md', '.cfg', '.ini', '.xml', '.html', '.js', '.css']
    
    # 遍历项目目录
    for root, dirs, files in os.walk('.'):
        # 跳过一些不需要搜索的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            # 只搜索指定类型的文件
            if file_ext in file_extensions or file_ext == '':
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        
                    for line_num, line in enumerate(lines, 1):
                        if '余汉波' in line:
                            search_results.append({
                                'file': file_path,
                                'line_number': line_num,
                                'line_content': line.strip(),
                                'context_before': lines[max(0, line_num-2):line_num-1] if line_num > 1 else [],
                                'context_after': lines[line_num:min(len(lines), line_num+2)] if line_num < len(lines) else []
                            })
                            
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    return search_results

def generate_detailed_report(search_results):
    """生成详细报告"""
    print(f"\n📊 搜索结果统计")
    print("=" * 60)
    
    if not search_results:
        print("❌ 未找到包含余汉波的内容")
        return
    
    print(f"✅ 找到 {len(search_results)} 处包含余汉波的位置")
    print(f"📁 涉及 {len(set(result['file'] for result in search_results))} 个文件")
    
    print(f"\n📋 详细定位信息")
    print("=" * 60)
    
    for i, result in enumerate(search_results, 1):
        print(f"\n🎯 位置 {i}:")
        print(f"📁 文件: {result['file']}")
        print(f"📍 行号: {result['line_number']}")
        print(f"📝 内容: {result['line_content']}")
        
        if result['context_before']:
            print(f"⬆️ 上下文（前）:")
            for j, line in enumerate(result['context_before']):
                print(f"   {result['line_number']-len(result['context_before'])+j}: {line.strip()}")
        
        if result['context_after']:
            print(f"⬇️ 上下文（后）:")
            for j, line in enumerate(result['context_after']):
                print(f"   {result['line_number']+j+1}: {line.strip()}")
        
        print("-" * 40)

def analyze_usage_context(search_results):
    """分析使用上下文"""
    print(f"\n使用上下文分析")
    print("=" * 60)
    
    contexts = {
        'author': [],
        'documentation': [],
        'title': [],
        'other': []
    }
    
    for result in search_results:
        content = result['line_content'].lower()
        
        if 'author' in content or '作者' in content:
            contexts['author'].append(result)
        elif any(keyword in content for keyword in ['文档', 'document', '介绍', '详细']):
            contexts['documentation'].append(result)
        elif any(keyword in content for keyword in ['title', '标题', '名称']):
            contexts['title'].append(result)
        else:
            contexts['other'].append(result)
    
    for context_type, results in contexts.items():
        if results:
            print(f"\n📌 {context_type.upper()} 上下文 ({len(results)} 处):")
            for result in results:
                print(f"   📁 {result['file']}:{result['line_number']}")
                print(f"   📝 {result['line_content']}")

def create_modification_plan(search_results):
    """创建修改计划"""
    print(f"\n📋 修改计划")
    print("=" * 60)
    
    if not search_results:
        print("❌ 没有需要修改的内容")
        return
    
    print("🎯 需要修改的文件:")
    
    files_to_modify = {}
    for result in search_results:
        file_path = result['file']
        if file_path not in files_to_modify:
            files_to_modify[file_path] = []
        files_to_modify[file_path].append(result)
    
    for file_path, results in files_to_modify.items():
        print(f"\n📁 {file_path}")
        print(f"   🔢 包含 {len(results)} 处余汉波")
        
        for result in results:
            print(f"   📍 行 {result['line_number']}: {result['line_content']}")
        
        # 建议修改方案
        if 'settings.json' in file_path:
            print(f"   💡 建议: 修改author字段")
        elif '.txt' in file_path:
            print(f"   💡 建议: 更新文档中的作者信息")
        elif '.py' in file_path:
            print(f"   💡 建议: 检查是否为注释或字符串中的作者信息")
        else:
            print(f"   💡 建议: 根据文件类型确定修改方案")

def main():
    """主函数"""
    print("🚨 全项目余汉波搜索报告")
    print("=" * 80)
    print("搜索项目中所有包含余汉波的文件和位置")
    print("=" * 80)
    
    # 执行搜索
    search_results = search_yuhanbo_in_project()
    
    # 生成详细报告
    generate_detailed_report(search_results)
    
    # 分析使用上下文
    analyze_usage_context(search_results)
    
    # 创建修改计划
    create_modification_plan(search_results)
    
    print(f"\n{'='*80}")
    print("🎯 搜索完成")
    print("=" * 80)
    
    if search_results:
        print(f"✅ 总共找到 {len(search_results)} 处包含余汉波的位置")
        print(f"📁 涉及 {len(set(result['file'] for result in search_results))} 个文件")
        print(f"💡 请根据上述报告进行相应的修改")
    else:
        print("❌ 项目中未找到余汉波相关内容")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
