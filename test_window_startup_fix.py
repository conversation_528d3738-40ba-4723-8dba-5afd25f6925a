#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试窗口启动位置修复效果
验证窗口是否直接在正确位置显示，无位置跳跃
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
import time
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from window_manager import setup_main_window, setup_test_window, WindowManager


class WindowStartupTester:
    """窗口启动测试器"""
    
    def __init__(self):
        self.position_log = []
        self.start_time = time.time()
    
    def log_position(self, root, event_name):
        """记录窗口位置"""
        try:
            geometry = root.geometry()
            current_time = time.time() - self.start_time
            
            # 解析几何信息
            if '+' in geometry:
                size_part = geometry.split('+')[0]
                pos_parts = geometry.split('+')[1:]
                position = f"+{'+'.join(pos_parts)}"
            else:
                size_part = geometry
                position = "未知"
            
            log_entry = {
                'time': current_time,
                'event': event_name,
                'geometry': geometry,
                'size': size_part,
                'position': position,
                'visible': root.winfo_viewable(),
                'mapped': root.winfo_ismapped()
            }
            
            self.position_log.append(log_entry)
            print(f"[{current_time:.3f}s] {event_name}: {geometry} (可见:{root.winfo_viewable()}, 映射:{root.winfo_ismapped()})")
            
        except Exception as e:
            print(f"记录位置失败: {str(e)}")


def test_optimized_window_manager():
    """测试优化后的窗口管理器"""
    print("🔧 测试优化后的窗口管理器")
    print("=" * 50)
    
    tester = WindowStartupTester()
    
    # 创建窗口
    print("创建窗口...")
    root = tk.Tk()
    tester.log_position(root, "窗口创建后")
    
    # 使用优化的窗口管理器
    print("应用窗口管理器...")
    setup_main_window(root, "优化后的窗口启动测试")
    tester.log_position(root, "窗口管理器应用后")
    
    # 延迟检查
    def delayed_check():
        time.sleep(0.1)
        tester.log_position(root, "延迟0.1秒后")
        time.sleep(0.5)
        tester.log_position(root, "延迟0.6秒后")
        time.sleep(1.0)
        tester.log_position(root, "延迟1.6秒后")
        
        # 分析结果
        print(f"\n{'='*50}")
        print("📊 位置变化分析")
        print("=" * 50)
        
        positions = []
        for log in tester.position_log:
            if log['position'] != "未知":
                positions.append(log['position'])
        
        unique_positions = list(dict.fromkeys(positions))
        
        print(f"位置变化序列: {' -> '.join(unique_positions)}")
        print(f"位置变化次数: {len(unique_positions)}")
        
        if len(unique_positions) <= 1:
            print("✅ 完美！窗口直接在正确位置显示，无位置跳跃")
        elif len(unique_positions) == 2:
            print("⚠️ 轻微改善：仍有1次位置变化")
        else:
            print("❌ 仍有多次位置跳跃")
        
        # 检查最终位置是否居中
        final_position = positions[-1] if positions else "未知"
        if "1260+420" in final_position or "1200+400" in final_position:
            print("✅ 最终位置正确居中")
        else:
            print(f"⚠️ 最终位置: {final_position}")
        
        root.after(1000, root.destroy)
    
    threading.Thread(target=delayed_check, daemon=True).start()
    
    # 运行主循环
    root.mainloop()
    
    return tester


def test_multiple_windows():
    """测试多个窗口的启动效果"""
    print(f"\n{'='*50}")
    print("🔧 测试多个窗口启动")
    print("=" * 50)
    
    windows = []
    testers = []
    
    # 创建3个窗口
    for i in range(3):
        print(f"\n创建第{i+1}个窗口...")
        
        tester = WindowStartupTester()
        testers.append(tester)
        
        root = tk.Tk()
        tester.log_position(root, f"窗口{i+1}创建后")
        
        if i == 0:
            setup_main_window(root, f"主窗口测试 {i+1}")
        else:
            setup_test_window(root, f"测试窗口 {i+1}")
        
        tester.log_position(root, f"窗口{i+1}设置后")
        
        windows.append(root)
        
        # 延迟检查
        def make_delayed_check(window, tester, index):
            def delayed_check():
                time.sleep(0.2)
                tester.log_position(window, f"窗口{index+1}延迟检查")
                
                # 分析这个窗口的位置变化
                positions = []
                for log in tester.position_log:
                    if log['position'] != "未知":
                        positions.append(log['position'])
                
                unique_positions = list(dict.fromkeys(positions))
                print(f"窗口{index+1}位置变化: {len(unique_positions)}次")
                
                if index == 2:  # 最后一个窗口
                    # 关闭所有窗口
                    for w in windows:
                        w.after(2000, w.destroy)
            
            return delayed_check
        
        threading.Thread(target=make_delayed_check(root, tester, i), daemon=True).start()
    
    # 运行所有窗口
    for root in windows:
        root.after(100, lambda: None)  # 确保窗口显示
    
    # 主循环
    windows[0].mainloop()
    
    return testers


def create_visual_test_window():
    """创建可视化测试窗口"""
    print(f"\n{'='*50}")
    print("🎨 创建可视化测试窗口")
    print("=" * 50)
    
    root = tk.Tk()
    setup_main_window(root, "窗口启动位置修复验证")
    
    # 创建界面
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="窗口启动位置修复验证", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 状态显示
    status_frame = ttk.LabelFrame(main_frame, text="修复状态", padding=10)
    status_frame.pack(fill=tk.X, pady=(0, 20))
    
    ttk.Label(status_frame, text="✅ 已应用优化的窗口管理器", font=("Arial", 12)).pack(anchor=tk.W)
    ttk.Label(status_frame, text="✅ 窗口创建时立即隐藏", font=("Arial", 12)).pack(anchor=tk.W)
    ttk.Label(status_frame, text="✅ 避免过早调用 update_idletasks()", font=("Arial", 12)).pack(anchor=tk.W)
    ttk.Label(status_frame, text="✅ 设置完成后统一显示", font=("Arial", 12)).pack(anchor=tk.W)
    
    # 测试按钮
    test_frame = ttk.LabelFrame(main_frame, text="测试功能", padding=10)
    test_frame.pack(fill=tk.X, pady=(0, 20))
    
    def test_new_main_window():
        new_root = tk.Tk()
        setup_main_window(new_root, "新主窗口测试")
        
        # 添加简单内容
        frame = ttk.Frame(new_root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        ttk.Label(frame, text="这是一个新的主窗口", font=("Arial", 14)).pack(pady=20)
        ttk.Button(frame, text="关闭", command=new_root.destroy).pack()
    
    def test_new_test_window():
        new_root = tk.Tk()
        setup_test_window(new_root, "新测试窗口")
        
        # 添加简单内容
        frame = ttk.Frame(new_root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        ttk.Label(frame, text="这是一个新的测试窗口", font=("Arial", 14)).pack(pady=20)
        ttk.Button(frame, text="关闭", command=new_root.destroy).pack()
    
    ttk.Button(test_frame, text="测试新主窗口", command=test_new_main_window).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(test_frame, text="测试新测试窗口", command=test_new_test_window).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(test_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT)
    
    # 说明文本
    help_frame = ttk.LabelFrame(main_frame, text="修复说明", padding=10)
    help_frame.pack(fill=tk.BOTH, expand=True)
    
    help_text = tk.Text(help_frame, wrap=tk.WORD, height=12)
    help_text.pack(fill=tk.BOTH, expand=True)
    help_text.insert(tk.END, """
窗口启动位置修复详情:

问题原因:
• root.update_idletasks() 在计算居中位置时被过早调用
• 导致窗口在设置geometry之前就显示在默认位置
• 然后再移动到正确的居中位置，造成视觉跳跃

修复方案:
• 在窗口设置过程中立即隐藏窗口 (root.withdraw())
• 使用优化的居中计算方法，避免过早调用 update_idletasks()
• 所有设置完成后统一显示窗口 (root.deiconify())
• 保持向后兼容性，提供原始方法作为备选

✅ 修复效果:
• 窗口直接在正确位置显示，无位置跳跃
• 用户体验更加流畅和专业
• 保持所有原有功能不变
• 支持多窗口同时创建

测试方法:
• 点击上方按钮创建新窗口
• 观察窗口是否直接在正确位置显示
• 检查是否有位置跳跃现象
• 验证窗口功能是否正常

性能提升:
• 减少不必要的窗口重绘
• 降低CPU使用率
• 提升启动速度
• 改善用户体验
    """)
    help_text.config(state=tk.DISABLED)
    
    return root


def main():
    """主测试函数"""
    print("🔧 窗口启动位置修复效果测试")
    print("=" * 60)
    
    # 测试1: 优化后的窗口管理器
    try:
        test_optimized_window_manager()
    except Exception as e:
        print(f"测试1失败: {str(e)}")
    
    # 测试2: 多窗口启动
    try:
        test_multiple_windows()
    except Exception as e:
        print(f"测试2失败: {str(e)}")
    
    # 测试3: 可视化验证
    try:
        print(f"\n{'='*50}")
        print("🎨 启动可视化验证窗口...")
        visual_window = create_visual_test_window()
        visual_window.mainloop()
    except Exception as e:
        print(f"可视化测试失败: {str(e)}")
    
    print(f"\n{'='*60}")
    print("✅ 窗口启动位置修复测试完成")
    print("📝 请观察窗口是否直接在正确位置显示，无位置跳跃")


if __name__ == "__main__":
    main()
