#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证新增内容是否都添加成功
"""

import os
import sys

def check_files():
    """检查新增文件"""
    print('检查新增文件状态...')
    print('=' * 50)

    files_to_check = [
        ('window_manager.py', '窗口管理器核心文件'),
        ('test_window_manager.py', '窗口管理器测试文件'),
        ('window_config.json', '窗口配置文件'),
        ('窗口位置管理系统实现报告.md', '实现报告文档'),
        ('qmt_ths/window_manager.py', 'qmt_ths目录中的窗口管理器'),
        ('verify_new_content.py', '本验证脚本')
    ]

    success_count = 0
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f'{description}: 存在 ({size} 字节)')
            success_count += 1
        else:
            print(f'{description}: 不存在')

    print(f'\n文件检查结果: {success_count}/{len(files_to_check)} 个文件存在')
    return success_count == len(files_to_check)

def check_imports():
    """检查导入功能"""
    print('\n🔧 检查导入功能...')
    print('=' * 50)
    
    # 测试根目录窗口管理器导入
    try:
        from window_manager import WindowManager
        print('✅ 根目录窗口管理器导入成功')
        
        wm = WindowManager()
        config = wm.load_window_config()
        print(f'✅ 配置加载成功: {list(config.keys())}')
        
    except Exception as e:
        print(f'❌ 根目录导入失败: {str(e)}')
        return False

    # 测试qmt_ths目录窗口管理器导入
    try:
        sys.path.append('./qmt_ths')
        from window_manager import WindowManager as WM2
        print('✅ qmt_ths目录窗口管理器导入成功')
        
    except Exception as e:
        print(f'❌ qmt_ths目录导入失败: {str(e)}')
        return False
    
    return True

def check_main_program_modifications():
    """检查主程序修改"""
    print('\n🔧 检查主程序修改...')
    print('=' * 50)
    
    main_file = 'qmt_ths/tonghuashun_gui.py'
    if not os.path.exists(main_file):
        print(f'❌ 主程序文件不存在: {main_file}')
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ('from window_manager import WindowManager', '窗口管理器导入'),
        ('self.window_manager = WindowManager()', '窗口管理器初始化'),
        ('self.window_manager.setup_window', '窗口设置调用'),
        ('def get_current_window_settings', '获取窗口设置方法'),
        ('def load_window_settings', '加载窗口设置方法'),
        ('window_settings', '窗口设置配置项')
    ]
    
    success_count = 0
    for check, description in checks:
        if check in content:
            print(f'✅ {description}: 已实现')
            success_count += 1
        else:
            print(f'❌ {description}: 未找到')
    
    print(f'\n📊 主程序修改检查: {success_count}/{len(checks)} 项完成')
    return success_count == len(checks)

def check_config_file():
    """检查配置文件内容"""
    print('\n🔧 检查配置文件内容...')
    print('=' * 50)
    
    config_file = 'window_config.json'
    if not os.path.exists(config_file):
        print(f'❌ 配置文件不存在: {config_file}')
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['main_window', 'test_window']
        success_count = 0
        
        for key in required_keys:
            if key in config:
                print(f'✅ 配置项 {key}: 存在')
                window_config = config[key]
                
                # 检查必要的子项
                sub_keys = ['width', 'height', 'center_on_startup', 'remember_position']
                for sub_key in sub_keys:
                    if sub_key in window_config:
                        print(f'  ✅ {sub_key}: {window_config[sub_key]}')
                    else:
                        print(f'  ❌ {sub_key}: 缺失')
                
                success_count += 1
            else:
                print(f'❌ 配置项 {key}: 不存在')
        
        print(f'\n📊 配置文件检查: {success_count}/{len(required_keys)} 项完成')
        return success_count == len(required_keys)
        
    except Exception as e:
        print(f'❌ 配置文件解析失败: {str(e)}')
        return False

def main():
    """主验证函数"""
    print('🔧 新增内容验证检查')
    print('=' * 60)
    
    results = []
    
    # 检查文件
    results.append(('文件检查', check_files()))
    
    # 检查导入
    results.append(('导入功能', check_imports()))
    
    # 检查主程序修改
    results.append(('主程序修改', check_main_program_modifications()))
    
    # 检查配置文件
    results.append(('配置文件', check_config_file()))
    
    # 总结
    print(f'\n{"="*60}')
    print('📊 验证结果总结')
    print('=' * 60)
    
    success_count = 0
    for name, result in results:
        status = '✅ 成功' if result else '❌ 失败'
        print(f'{name}: {status}')
        if result:
            success_count += 1
    
    print(f'\n🎯 总体完成度: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)')
    
    if success_count == len(results):
        print('🎉 所有新增内容都已成功添加！')
        return True
    else:
        print('⚠️ 部分内容可能存在问题，需要进一步检查')
        return False

if __name__ == "__main__":
    main()
