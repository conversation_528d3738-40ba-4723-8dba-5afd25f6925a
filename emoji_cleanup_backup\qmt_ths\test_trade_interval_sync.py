#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易间隔UI界面与实际执行联动机制测试脚本

测试内容：
1. UI界面修改验证
2. 实时生效测试
3. 配置持久化验证
4. 日志输出确认
5. 边界情况测试
"""

import tkinter as tk
from tkinter import ttk
import json
import os
import time
from datetime import datetime
import sys
import logging
import traceback

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('test_trade_interval')

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tonghuashun_gui import TongHuaShunTrader

class TradeIntervalTester:
    def __init__(self):
        self.test_results = []
        self.trader = None
        
    def log_test_result(self, test_name, result, details=""):
        """记录测试结果"""
        status = "通过" if result else "失败"
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.test_results.append({
            'time': timestamp,
            'test': test_name,
            'result': result,
            'status': status,
            'details': details
        })
        logger.info(f"{test_name}: {status}")
        if details:
            logger.info(f"    详情: {details}")
    
    def test_ui_interface_modification(self):
        """测试1: UI界面修改验证"""
        logger.info("=== 测试1: UI界面修改验证 ===")
        
        try:
            # 创建交易器实例
            self.trader = TongHuaShunTrader()
            
            # 检查默认值
            default_value = self.trader.trade_interval_entry.get()
            self.log_test_result(
                "UI界面默认值检查", 
                default_value == "1.0",
                f"期望: 1.0, 实际: {default_value}"
            )
            
            # 测试修改UI值
            test_values = ["2.5", "5", "0.5", "10"]
            for test_value in test_values:
                # 清空并设置新值
                self.trader.trade_interval_entry.delete(0, tk.END)
                self.trader.trade_interval_entry.insert(0, test_value)
                
                # 读取值
                read_value = self.trader.trade_interval_entry.get()
                self.log_test_result(
                    f"UI修改测试 ({test_value})",
                    read_value == test_value,
                    f"设置: {test_value}, 读取: {read_value}"
                )
            
            return True
            
        except Exception as e:
            self.log_test_result("UI界面修改验证", False, f"异常: {str(e)}")
            return False
    
    def test_real_time_effectiveness(self):
        """测试2: 实时生效测试"""
        logger.info("\n=== 测试2: 实时生效测试 ===")
        
        try:
            if not self.trader:
                self.trader = TongHuaShunTrader()
            
            # 测试validate_trade_interval方法是否实时读取UI值
            test_intervals = ["1", "0.5", "30", "60"]
            
            for interval in test_intervals:
                # 设置UI值
                self.trader.trade_interval_entry.delete(0, tk.END)
                self.trader.trade_interval_entry.insert(0, interval)
                
                # 调用验证方法
                is_valid, error_msg = self.trader.validate_trade_interval()
                
                # 检查是否正确读取了新值
                expected_valid = 0.1 <= float(interval) <= 3600
                self.log_test_result(
                    f"实时验证测试 ({interval}秒)",
                    is_valid == expected_valid,
                    f"间隔: {interval}, 验证结果: {is_valid}, 错误信息: {error_msg}"
                )
            
            return True
            
        except Exception as e:
            self.log_test_result("实时生效测试", False, f"异常: {str(e)}")
            return False
    
    def test_config_persistence(self):
        """测试3: 配置持久化验证"""
        logger.info("\n=== 测试3: 配置持久化验证 ===")
        
        try:
            if not self.trader:
                self.trader = TongHuaShunTrader()
            
            # 备份原配置文件
            config_file = "trader_config.json"
            backup_file = "trader_config_backup.json"
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    original_config = f.read()
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_config)
            
            # 测试保存配置
            test_interval = "3.5"
            self.trader.trade_interval_entry.delete(0, tk.END)
            self.trader.trade_interval_entry.insert(0, test_interval)
            
            # 保存配置
            self.trader.save_config()
            
            # 检查配置文件
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                saved_interval = config.get('trade_interval', '')
                self.log_test_result(
                    "配置保存测试",
                    saved_interval == test_interval,
                    f"设置: {test_interval}, 保存: {saved_interval}"
                )
                
                # 测试加载配置
                self.trader.trade_interval_entry.delete(0, tk.END)
                self.trader.trade_interval_entry.insert(0, "999")  # 设置一个不同的值
                
                # 重新加载配置
                self.trader.load_config()
                
                loaded_value = self.trader.trade_interval_entry.get()
                self.log_test_result(
                    "配置加载测试",
                    loaded_value == test_interval,
                    f"保存: {test_interval}, 加载: {loaded_value}"
                )
            else:
                self.log_test_result("配置保存测试", False, "配置文件未创建")
            
            # 恢复原配置文件
            if os.path.exists(backup_file):
                with open(backup_file, 'r', encoding='utf-8') as f:
                    original_config = f.read()
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(original_config)
                os.remove(backup_file)
            
            return True
            
        except Exception as e:
            self.log_test_result("配置持久化验证", False, f"异常: {str(e)}")
            return False
    
    def test_boundary_cases(self):
        """测试5: 边界情况测试"""
        logger.info("\n=== 测试5: 边界情况测试 ===")
        
        try:
            if not self.trader:
                self.trader = TongHuaShunTrader()
            
            # 测试边界值和无效值
            test_cases = [
                ("0.1", True, "最小有效值"),
                ("3600", True, "最大有效值"),
                ("0.05", False, "小于最小值"),
                ("3601", False, "大于最大值"),
                ("-1", False, "负数"),
                ("0", False, "零值"),
                ("abc", False, "非数字"),
                ("", False, "空值"),
                ("1.5.5", False, "无效格式"),
                ("1e10", False, "科学计数法超范围")
            ]
            
            for test_value, expected_valid, description in test_cases:
                # 设置测试值
                self.trader.trade_interval_entry.delete(0, tk.END)
                self.trader.trade_interval_entry.insert(0, test_value)
                
                # 验证
                is_valid, error_msg = self.trader.validate_trade_interval()
                
                self.log_test_result(
                    f"边界测试: {description}",
                    is_valid == expected_valid,
                    f"值: '{test_value}', 期望: {expected_valid}, 实际: {is_valid}, 错误: {error_msg}"
                )
            
            return True
            
        except Exception as e:
            self.log_test_result("边界情况测试", False, f"异常: {str(e)}")
            return False
    
    def test_log_output_verification(self):
        """测试4: 日志输出确认"""
        logger.info("\n=== 测试4: 日志输出确认 ===")
        
        try:
            if not self.trader:
                self.trader = TongHuaShunTrader()
            
            # 设置交易间隔
            test_interval = "2.5"
            self.trader.trade_interval_entry.delete(0, tk.END)
            self.trader.trade_interval_entry.insert(0, test_interval)
            
            # 模拟开始交易时的日志输出
            self.trader.block_name_entry.delete(0, tk.END)
            self.trader.block_name_entry.insert(0, "TEST")
            self.trader.account_entry.delete(0, tk.END)
            self.trader.account_entry.insert(0, "123456")
            
            # 获取日志输出（模拟start_trading中的日志）
            block_name = self.trader.block_name_entry.get()
            account = self.trader.account_entry.get()
            total_amount = self.trader.total_amount_entry.get()
            single_amount = self.trader.single_amount_entry.get()
            trade_interval = self.trader.trade_interval_entry.get()
            price_type = self.trader.price_type.get()
            price_adjust = self.trader.price_adjust.get()
            
            expected_log = f"开始交易 - 账户: {account}, 板块: {block_name}, 总金额: {total_amount}, " \
                          f"单笔金额: {single_amount}, 交易间隔: {trade_interval}秒, " \
                          f"价格类型: {price_type}, 价格调整: {price_adjust}%"
            
            # 检查日志是否包含正确的交易间隔
            contains_interval = trade_interval in expected_log
            self.log_test_result(
                "日志输出包含交易间隔",
                contains_interval,
                f"交易间隔: {trade_interval}, 日志: {expected_log[:100]}..."
            )
            
            return True
            
        except Exception as e:
            self.log_test_result("日志输出确认", False, f"异常: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始交易间隔UI界面与实际执行联动机制测试")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # 运行所有测试
        tests = [
            self.test_ui_interface_modification,
            self.test_real_time_effectiveness,
            self.test_config_persistence,
            self.test_log_output_verification,
            self.test_boundary_cases
        ]
        
        passed = 0
        total = 0
        
        for test_func in tests:
            try:
                result = test_func()
                if result:
                    passed += 1
                total += 1
            except Exception as e:
                logger.error(f"测试执行异常: {str(e)}")
                logger.error(traceback.format_exc())
                total += 1
        
        # 清理资源
        if self.trader and self.trader.root:
            try:
                self.trader.root.quit()
                self.trader.root.destroy()
            except Exception:
                pass
        
        # 输出测试结果
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info("\n" + "=" * 60)
        logger.info("测试结果汇总:")
        logger.info(f"总测试数: {total}")
        logger.info(f"通过数: {passed}")
        logger.info(f"失败数: {total - passed}")
        logger.info(f"成功率: {(passed/total*100):.1f}%" if total > 0 else "0%")
        logger.info(f"测试耗时: {duration:.2f}秒")
        
        logger.info("\n详细结果:")
        for result in self.test_results:
            logger.info(f"[{result['time']}] {result['test']}: {result['status']}")
            if result['details']:
                logger.info(f"    {result['details']}")
        
        return passed == total

if __name__ == "__main__":
    tester = TradeIntervalTester()
    success = tester.run_all_tests()
    
    if success:
        logger.info("\n🎉 所有测试通过！UI界面与实际执行联动机制正常工作。")
    else:
        logger.info("\n部分测试失败，需要检查相关问题。")
    
    input("\n按回车键退出...")
