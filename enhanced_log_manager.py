#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的日志管理器
实现前台简化日志显示功能
"""

import re
import json
import threading
from datetime import datetime
from collections import deque


class EnhancedLogManager:
    """增强的日志管理器 - 支持双层日志系统"""
    
    def __init__(self, max_detailed_logs=1000, max_simplified_logs=100):
        # 详细日志（后台保存）
        self.detailed_logs = deque(maxlen=max_detailed_logs)
        
        # 简化日志（前台显示）
        self.simplified_logs = deque(maxlen=max_simplified_logs)
        
        # 已显示的股票代码（避免重复显示）
        self.displayed_stocks = set()
        
        # 前台更新回调函数列表
        self.frontend_callbacks = []
        
        # 调试模式回调函数列表
        self.debug_callbacks = []
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 股票结果日志的正则表达式
        self.stock_result_pattern = re.compile(r'(\d{6})\([^)]+\):.*委托价')
        
        # 调试日志的正则表达式
        self.debug_patterns = [
            re.compile(r'获取.*行情数据'),
            re.compile(r'详细行情获取调试'),
            re.compile(r'准备尝试的代码格式'),
            re.compile(r'尝试获取代码'),
            re.compile(r'获取到行情数据'),
            re.compile(r'最新价:'),
            re.compile(r'涨停价:'),
            re.compile(r'跌停价:'),
            re.compile(r'买一价:'),
            re.compile(r'卖一价:'),
            re.compile(r'完整数据:'),
            re.compile(r'✅.*最新价格:')
        ]
    
    def add_frontend_callback(self, callback):
        """添加前台更新回调函数"""
        with self.lock:
            self.frontend_callbacks.append(callback)
    
    def add_debug_callback(self, callback):
        """添加调试功能回调函数"""
        with self.lock:
            self.debug_callbacks.append(callback)
    
    def is_stock_result_log(self, message):
        """判断是否为股票结果日志"""
        return bool(self.stock_result_pattern.search(message))
    
    def is_debug_log(self, message):
        """判断是否为调试日志"""
        return any(pattern.search(message) for pattern in self.debug_patterns)
    
    def extract_stock_code(self, message):
        """从日志消息中提取股票代码"""
        match = self.stock_result_pattern.search(message)
        return match.group(1) if match else None
    
    def log_message(self, message, level="INFO"):
        """统一日志接口 - 替换原有的log_message方法"""
        with self.lock:
            timestamp = datetime.now()
            
            # 1. 保存详细日志（所有日志都保存）
            detailed_entry = {
                'timestamp': timestamp,
                'message': message,
                'level': level,
                'is_stock_result': self.is_stock_result_log(message),
                'is_debug': self.is_debug_log(message)
            }
            self.detailed_logs.append(detailed_entry)
            
            # 2. 处理股票结果日志（前台显示）
            if self.is_stock_result_log(message):
                stock_code = self.extract_stock_code(message)
                
                # 检查是否已显示过这只股票
                if stock_code and stock_code not in self.displayed_stocks:
                    self.displayed_stocks.add(stock_code)
                    
                    # 添加到简化日志
                    simplified_entry = {
                        'timestamp': timestamp,
                        'message': message,
                        'level': level,
                        'stock_code': stock_code
                    }
                    self.simplified_logs.append(simplified_entry)
                    
                    # 通知前台更新
                    self._notify_frontend_update(simplified_entry)
            
            # 3. 通知调试功能更新（所有日志）
            self._notify_debug_update(detailed_entry)
    
    def _notify_frontend_update(self, log_entry):
        """通知前台更新显示"""
        for callback in self.frontend_callbacks:
            try:
                callback(log_entry)
            except Exception as e:
                print(f"前台回调执行失败: {e}")
    
    def _notify_debug_update(self, log_entry):
        """通知调试功能更新"""
        for callback in self.debug_callbacks:
            try:
                callback(log_entry)
            except Exception as e:
                print(f"调试回调执行失败: {e}")
    
    def get_simplified_logs(self, count=None):
        """获取简化日志"""
        with self.lock:
            if count is None:
                return list(self.simplified_logs)
            else:
                return list(self.simplified_logs)[-count:]
    
    def get_detailed_logs(self, count=None, filter_type=None):
        """获取详细日志"""
        with self.lock:
            logs = list(self.detailed_logs)
            
            # 应用过滤器
            if filter_type == 'stock_only':
                logs = [log for log in logs if log['is_stock_result']]
            elif filter_type == 'debug_only':
                logs = [log for log in logs if log['is_debug']]
            elif filter_type == 'non_debug':
                logs = [log for log in logs if not log['is_debug']]
            
            # 返回指定数量
            if count is None:
                return logs
            else:
                return logs[-count:]
    
    def clear_displayed_stocks(self):
        """清空已显示股票列表（用于新的交易日）"""
        with self.lock:
            self.displayed_stocks.clear()
    
    def clear_simplified_logs(self):
        """清空简化日志"""
        with self.lock:
            self.simplified_logs.clear()
    
    def clear_detailed_logs(self):
        """清空详细日志"""
        with self.lock:
            self.detailed_logs.clear()
    
    def clear_all_logs(self):
        """清空所有日志"""
        with self.lock:
            self.detailed_logs.clear()
            self.simplified_logs.clear()
            self.displayed_stocks.clear()
    
    def get_stats(self):
        """获取日志统计信息"""
        with self.lock:
            return {
                'detailed_count': len(self.detailed_logs),
                'simplified_count': len(self.simplified_logs),
                'displayed_stocks_count': len(self.displayed_stocks),
                'displayed_stocks': list(self.displayed_stocks)
            }
    
    def export_logs(self, filename, log_type='detailed'):
        """导出日志到文件"""
        try:
            if log_type == 'detailed':
                logs = self.get_detailed_logs()
            else:
                logs = self.get_simplified_logs()
            
            export_data = []
            for log in logs:
                export_data.append({
                    'timestamp': log['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                    'message': log['message'],
                    'level': log['level']
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True, f"日志已导出到 {filename}"
        
        except Exception as e:
            return False, f"导出失败: {str(e)}"


class LogDisplayFormatter:
    """日志显示格式化器"""
    
    @staticmethod
    def format_for_frontend(log_entry):
        """格式化用于前台显示"""
        timestamp = log_entry['timestamp'].strftime('%H:%M:%S')
        message = log_entry['message']
        return f"[{timestamp}] {message}"
    
    @staticmethod
    def format_for_debug(log_entry):
        """格式化用于调试显示"""
        timestamp = log_entry['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
        level = log_entry['level']
        message = log_entry['message']
        return f"[{timestamp}] [{level}] {message}"
    
    @staticmethod
    def format_stock_summary(logs):
        """格式化股票汇总信息"""
        stock_count = len(logs)
        if stock_count == 0:
            return "暂无股票信息"
        
        latest_log = logs[-1]
        timestamp = latest_log['timestamp'].strftime('%H:%M:%S')
        
        return f"共{stock_count}只股票，最新更新: {timestamp}"


# 全局日志管理器实例
enhanced_log_manager = EnhancedLogManager()


def get_log_manager():
    """获取全局日志管理器实例"""
    return enhanced_log_manager


def setup_enhanced_logging(trader_instance):
    """为交易器实例设置增强日志功能"""
    # 保存原始的log_message方法
    trader_instance._original_log_message = trader_instance.log_message
    
    # 替换为增强的日志方法
    def enhanced_log_message(message, level="INFO"):
        # 调用增强日志管理器
        enhanced_log_manager.log_message(message, level)
        
        # 如果需要保持原有行为，也可以调用原始方法
        # trader_instance._original_log_message(message, level)
    
    trader_instance.log_message = enhanced_log_message
    
    return enhanced_log_manager
