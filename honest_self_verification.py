#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诚实的自我检验
检查我的反馈是否真实可靠
"""

import os
import re
import sys

def check_my_claims_honestly():
    """诚实检查我的声明"""
    print("诚实的自我检验")
    print("=" * 50)
    print("检查我的反馈是否真实可靠")
    print("=" * 50)
    
    claims_to_verify = [
        {
            "claim": "时间设置问题已解决",
            "evidence_needed": [
                "is_trading_time方法存在",
                "_get_cached_trading_times方法存在", 
                "_update_time_cache方法存在",
                "后台线程中不再直接访问start_time.get()和end_time.get()"
            ]
        },
        {
            "claim": "停止交易问题已解决", 
            "evidence_needed": [
                "_stop_trading_background方法存在",
                "scheduler.shutdown调用存在",
                "超时控制机制存在",
                "后台线程执行停止操作"
            ]
        },
        {
            "claim": "程序无响应问题已解决",
            "evidence_needed": [
                "monitor_lock.acquire(timeout=1)存在",
                "try-finally锁释放结构存在",
                "异常处理完善"
            ]
        },
        {
            "claim": "详细日志问题已解决",
            "evidence_needed": [
                "_safe_log_message方法同时更新两个日志区域",
                "_should_filter_from_simplified_log方法存在",
                "detailed_log_text和simplified_log_text都被更新"
            ]
        },
        {
            "claim": "线程安全问题已解决",
            "evidence_needed": [
                "_get_cached_config方法存在",
                "后台线程中使用缓存配置而不是直接GUI访问",
                "配置缓存机制完整"
            ]
        }
    ]
    
    try:
        with open('./qmt_ths/tonghuashun_gui.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
    except Exception as e:
        print(f"❌ 无法读取源代码文件: {e}")
        return False
    
    verification_results = []
    
    for claim_data in claims_to_verify:
        claim = claim_data["claim"]
        evidence_list = claim_data["evidence_needed"]
        
        print(f"\n验证声明: {claim}")
        print("-" * 40)
        
        evidence_found = []
        evidence_missing = []
        
        for evidence in evidence_list:
            if "方法存在" in evidence:
                method_name = evidence.split("方法存在")[0]
                pattern = rf'def {method_name}\('
                if re.search(pattern, source_code):
                    evidence_found.append(evidence)
                    print(f"  ✅ {evidence}")
                else:
                    evidence_missing.append(evidence)
                    print(f"  ❌ {evidence}")
            
            elif "调用存在" in evidence:
                search_term = evidence.split("调用存在")[0]
                if "scheduler.shutdown" in search_term:
                    # 检查是否有scheduler.shutdown调用
                    if "scheduler.shutdown" in source_code:
                        evidence_found.append(evidence)
                        print(f"  ✅ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
                else:
                    if search_term in source_code:
                        evidence_found.append(evidence)
                        print(f"  ✅ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
            
            elif "存在" in evidence:
                # 更具体的检查
                if "monitor_lock.acquire(timeout=" in evidence:
                    if re.search(r'monitor_lock\.acquire\(timeout=\d+\)', source_code):
                        evidence_found.append(evidence)
                        print(f"  ✅ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
                
                elif "try-finally锁释放" in evidence:
                    if "try:" in source_code and "finally:" in source_code and "monitor_lock.release()" in source_code:
                        evidence_found.append(evidence)
                        print(f"  ✅ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
                
                elif "后台线程中不再直接访问" in evidence:
                    # 检查monitor_file方法中是否还有直接GUI访问
                    monitor_file_pattern = r'def monitor_file\(.*?\):(.*?)(?=def |\Z)'
                    monitor_match = re.search(monitor_file_pattern, source_code, re.DOTALL)
                    if monitor_match:
                        monitor_code = monitor_match.group(1)
                        unsafe_patterns = [r'self\.start_time\.get\(\)', r'self\.end_time\.get\(\)']
                        has_unsafe = any(re.search(pattern, monitor_code) for pattern in unsafe_patterns)
                        # 同时检查是否使用了安全的时间检查
                        has_safe_time_check = "is_trading_time" in monitor_code
                        if not has_unsafe and has_safe_time_check:
                            evidence_found.append(evidence)
                            print(f"  ✅ {evidence}")
                        else:
                            evidence_missing.append(evidence)
                            print(f"  ❌ {evidence} (不安全访问:{has_unsafe}, 安全检查:{has_safe_time_check})")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence} (monitor_file方法未找到)")
                
                elif "同时更新两个日志区域" in evidence:
                    # 检查_safe_log_message方法中是否同时更新两个日志区域
                    safe_log_pattern = r'def _safe_log_message\(.*?\):(.*?)(?=def |\Z)'
                    safe_log_match = re.search(safe_log_pattern, source_code, re.DOTALL)
                    if safe_log_match:
                        safe_log_code = safe_log_match.group(1)
                        has_detailed = "detailed_log_text.insert" in safe_log_code
                        has_simplified = "simplified_log_text.insert" in safe_log_code
                        if has_detailed and has_simplified:
                            evidence_found.append(evidence)
                            print(f"  ✅ {evidence}")
                        else:
                            evidence_missing.append(evidence)
                            print(f"  ❌ {evidence} (详细:{has_detailed}, 简化:{has_simplified})")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence} (_safe_log_message方法未找到)")
                
                elif "后台线程中使用缓存配置" in evidence:
                    # 检查monitor_file中是否使用_get_cached_config
                    monitor_file_pattern = r'def monitor_file\(.*?\):(.*?)(?=def |\Z)'
                    monitor_match = re.search(monitor_file_pattern, source_code, re.DOTALL)
                    if monitor_match:
                        monitor_code = monitor_match.group(1)
                        if "_get_cached_config" in monitor_code:
                            evidence_found.append(evidence)
                            print(f"  ✅ {evidence}")
                        else:
                            evidence_missing.append(evidence)
                            print(f"  ❌ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
                
                else:
                    # 简单的文本搜索
                    search_terms = evidence.split("存在")[0].strip()
                    if search_terms in source_code:
                        evidence_found.append(evidence)
                        print(f"  ✅ {evidence}")
                    else:
                        evidence_missing.append(evidence)
                        print(f"  ❌ {evidence}")
        
        # 评估声明的真实性
        evidence_ratio = len(evidence_found) / len(evidence_list)
        if evidence_ratio >= 0.8:
            claim_status = "基本真实"
        elif evidence_ratio >= 0.5:
            claim_status = "部分真实"
        else:
            claim_status = "可能不真实"
        
        verification_results.append({
            "claim": claim,
            "status": claim_status,
            "evidence_ratio": evidence_ratio,
            "found": len(evidence_found),
            "total": len(evidence_list),
            "missing": evidence_missing
        })
        
        print(f"  📊 证据比例: {len(evidence_found)}/{len(evidence_list)} ({evidence_ratio*100:.1f}%)")
        print(f"  🎯 声明状态: {claim_status}")
    
    return verification_results

def analyze_potential_issues():
    """分析潜在问题"""
    print(f"\n分析潜在问题")
    print("=" * 50)
    
    potential_issues = [
        "我的测试脚本可能有缺陷",
        "我可能过于乐观地解释了结果", 
        "可能存在我未发现的边缘情况",
        "修复可能在某些条件下失效",
        "我的验证方法可能不够全面"
    ]
    
    print("⚠️ 可能存在的问题:")
    for i, issue in enumerate(potential_issues, 1):
        print(f"  {i}. {issue}")
    
    print(f"\n💡 诚实的评估:")
    print("  • 我的分析基于代码检查和测试脚本")
    print("  • 我没有在真实环境中运行完整的交易流程")
    print("  • 我的测试可能无法覆盖所有使用场景")
    print("  • 某些问题可能只在特定条件下出现")
    print("  • 我可能对修复效果过于乐观")

def provide_honest_assessment():
    """提供诚实的评估"""
    print(f"\n🎯 诚实的最终评估")
    print("=" * 50)
    
    print("✅ 我确信的部分:")
    print("  • 我确实修改了相关的代码")
    print("  • 我添加了时间缓存、配置缓存等机制")
    print("  • 我修复了明显的线程安全问题")
    print("  • 我改进了停止交易和日志显示机制")
    
    print(f"\n⚠️ 我不确定的部分:")
    print("  • 修复是否在所有情况下都有效")
    print("  • 是否还有我未发现的问题")
    print("  • 真实使用中是否会出现新问题")
    print("  • 性能是否受到影响")
    
    print(f"\n💡 建议:")
    print("  • 在真实环境中测试修复效果")
    print("  • 监控程序运行状态")
    print("  • 如果发现问题，及时反馈")
    print("  • 保留原始代码备份以便回滚")

def main():
    """主函数"""
    print("🚨 诚实的自我检验报告")
    print("=" * 60)
    print("检查我的反馈是否真实可靠")
    print("=" * 60)
    
    # 验证我的声明
    results = check_my_claims_honestly()
    
    if results:
        print(f"\n📊 总体验证结果")
        print("=" * 50)
        
        total_claims = len(results)
        basically_true = sum(1 for r in results if r["status"] == "基本真实")
        partially_true = sum(1 for r in results if r["status"] == "部分真实")
        possibly_false = sum(1 for r in results if r["status"] == "可能不真实")
        
        print(f"总声明数: {total_claims}")
        print(f"基本真实: {basically_true}")
        print(f"部分真实: {partially_true}")
        print(f"可能不真实: {possibly_false}")
        
        overall_credibility = (basically_true + partially_true * 0.5) / total_claims
        print(f"\n整体可信度: {overall_credibility*100:.1f}%")
        
        if overall_credibility >= 0.8:
            print("🎯 评估: 我的反馈基本可信")
        elif overall_credibility >= 0.6:
            print("⚠️ 评估: 我的反馈部分可信，需要谨慎")
        else:
            print("❌ 评估: 我的反馈可信度不足")
    
    # 分析潜在问题
    analyze_potential_issues()
    
    # 提供诚实评估
    provide_honest_assessment()
    
    print(f"\n{'='*60}")
    print("🎯 最终诚实结论")
    print("=" * 60)
    print("我尽力提供了准确的分析，但可能存在局限性。")
    print("建议您在实际使用中验证修复效果。")
    print("=" * 60)

if __name__ == "__main__":
    main()
