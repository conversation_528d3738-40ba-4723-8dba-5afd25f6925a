#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tkinter线程安全性真实测试
验证在后台线程中访问GUI组件是否真的危险
"""

import tkinter as tk
import threading
import time
import sys
from datetime import datetime

def test_tkinter_thread_safety():
    """测试tkinter在多线程环境下的行为"""
    print("🧪 测试tkinter线程安全性")
    print("=" * 50)
    
    # 创建GUI
    root = tk.Tk()
    root.title("线程安全测试")
    root.geometry("600x400")
    
    # 创建Entry组件
    entry = tk.Entry(root, width=50)
    entry.pack(pady=20)
    entry.insert(0, "初始值")
    
    # 创建显示区域
    text_area = tk.Text(root, height=15, width=70)
    text_area.pack(pady=10)
    
    # 测试结果
    test_results = {
        'success_count': 0,
        'error_count': 0,
        'errors': []
    }
    
    def background_worker():
        """后台线程工作函数"""
        for i in range(100):
            try:
                # 在后台线程中访问GUI组件
                value = entry.get()
                
                # 模拟一些处理
                processed_value = f"处理第{i}次: {value} - {datetime.now().strftime('%H:%M:%S.%f')}"
                
                # 尝试更新GUI（这是危险的操作）
                text_area.insert(tk.END, f"{processed_value}\n")
                text_area.see(tk.END)
                
                test_results['success_count'] += 1
                time.sleep(0.01)  # 短暂延迟
                
            except Exception as e:
                test_results['error_count'] += 1
                test_results['errors'].append(str(e))
                print(f"后台线程错误: {e}")
    
    def safe_background_worker():
        """安全的后台线程工作函数"""
        for i in range(100):
            try:
                # 在后台线程中只读取值（相对安全）
                value = entry.get()
                
                # 模拟处理
                processed_value = f"安全处理第{i}次: {value}"
                
                # 使用after方法安全更新GUI
                root.after(0, lambda msg=processed_value: text_area.insert(tk.END, f"{msg}\n"))
                root.after(0, lambda: text_area.see(tk.END))
                
                test_results['success_count'] += 1
                time.sleep(0.01)
                
            except Exception as e:
                test_results['error_count'] += 1
                test_results['errors'].append(str(e))
                print(f"安全后台线程错误: {e}")
    
    def start_unsafe_test():
        """启动不安全的测试"""
        text_area.delete(1.0, tk.END)
        text_area.insert(tk.END, "开始不安全的线程测试...\n")
        test_results['success_count'] = 0
        test_results['error_count'] = 0
        test_results['errors'] = []
        
        thread = threading.Thread(target=background_worker, daemon=True)
        thread.start()
    
    def start_safe_test():
        """启动安全的测试"""
        text_area.delete(1.0, tk.END)
        text_area.insert(tk.END, "开始安全的线程测试...\n")
        test_results['success_count'] = 0
        test_results['error_count'] = 0
        test_results['errors'] = []
        
        thread = threading.Thread(target=safe_background_worker, daemon=True)
        thread.start()
    
    def show_results():
        """显示测试结果"""
        result_text = f"""
测试结果：
成功次数: {test_results['success_count']}
错误次数: {test_results['error_count']}
错误列表: {test_results['errors'][:5]}  # 只显示前5个错误
"""
        text_area.insert(tk.END, result_text)
    
    # 创建按钮
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    tk.Button(button_frame, text="不安全测试", command=start_unsafe_test).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="安全测试", command=start_safe_test).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="显示结果", command=show_results).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="退出", command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    # 添加说明
    info_label = tk.Label(root, text="测试说明：不安全测试直接在后台线程操作GUI，安全测试使用after方法", 
                         wraplength=500, justify=tk.LEFT)
    info_label.pack(pady=5)
    
    print("✅ GUI测试程序已启动")
    print("📋 测试说明：")
    print("1. 点击'不安全测试'观察直接在后台线程操作GUI的效果")
    print("2. 点击'安全测试'观察使用after方法的效果")
    print("3. 在测试过程中可以修改输入框的值")
    print("4. 观察是否出现错误或程序崩溃")
    
    # 运行GUI
    root.mainloop()
    
    return test_results

def test_entry_get_thread_safety():
    """专门测试Entry.get()的线程安全性"""
    print(f"\n🧪 专门测试Entry.get()线程安全性")
    print("=" * 50)
    
    try:
        # 创建简单的GUI
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        entry = tk.Entry(root)
        entry.insert(0, "测试值")
        
        results = []
        errors = []
        
        def worker():
            for i in range(1000):
                try:
                    value = entry.get()
                    results.append(value)
                except Exception as e:
                    errors.append(str(e))
        
        # 启动多个线程同时读取
        threads = []
        for i in range(5):
            t = threading.Thread(target=worker)
            threads.append(t)
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        root.destroy()
        
        print(f"✅ 测试完成:")
        print(f"  成功读取次数: {len(results)}")
        print(f"  错误次数: {len(errors)}")
        print(f"  值的一致性: {'一致' if len(set(results)) <= 1 else '不一致'}")
        
        if errors:
            print(f"  错误示例: {errors[:3]}")
            return False
        else:
            print("  ✅ 没有发现错误")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_actual_risk():
    """分析实际风险"""
    print(f"\n分析实际风险")
    print("=" * 50)
    
    print("📋 tkinter线程安全性分析：")
    print()
    print("1. **读取操作（如entry.get()）**：")
    print("   - 理论上：不是线程安全的")
    print("   - 实际上：在大多数情况下不会立即崩溃")
    print("   - 风险：可能读取到不一致的数据")
    print()
    print("2. **写入操作（如text.insert()）**：")
    print("   - 理论上：非常危险")
    print("   - 实际上：可能导致程序崩溃或GUI冻结")
    print("   - 风险：高")
    print()
    print("3. **在我们的程序中**：")
    print("   - 主要是读取操作（entry.get()）")
    print("   - 配置通常在启动后不变")
    print("   - 实际风险：中等")
    print()
    print("4. **最佳实践**：")
    print("   - 使用配置缓存")
    print("   - 避免在后台线程直接访问GUI")
    print("   - 使用root.after()进行GUI更新")

def main():
    """主函数"""
    print("🚨 tkinter线程安全性深度分析")
    print("=" * 60)
    print("目标：验证线程安全问题是否真实存在")
    print("=" * 60)
    
    # 分析实际风险
    analyze_actual_risk()
    
    # 测试Entry.get()的线程安全性
    entry_safe = test_entry_get_thread_safety()
    
    print(f"\n{'='*60}")
    print("📊 分析结论")
    print("=" * 60)
    
    if entry_safe:
        print("✅ Entry.get()在测试中没有立即出现错误")
        print("⚠️ 但这不意味着它是线程安全的")
    else:
        print("❌ Entry.get()在测试中出现了错误")
    
    print("\n💡 结论：")
    print("1. 线程安全问题确实存在，但不一定立即显现")
    print("2. 在配置不变的情况下，风险相对较低")
    print("3. 为了代码质量和稳定性，建议修复")
    print("4. 可以通过配置缓存机制解决")
    
    print(f"\n{'='*60}")
    print("🎮 如果要进行GUI测试，请运行：")
    print("python tkinter_thread_safety_test.py --gui")
    
    # 检查是否要运行GUI测试
    if len(sys.argv) > 1 and sys.argv[1] == '--gui':
        test_tkinter_thread_safety()

if __name__ == "__main__":
    main()
