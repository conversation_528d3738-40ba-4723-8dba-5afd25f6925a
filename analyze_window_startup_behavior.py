#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析窗口启动时的位置变化行为
查找窗口先在左侧显示然后居中的原因
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
import time
import threading

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from window_manager import WindowManager, setup_main_window


class WindowStartupAnalyzer:
    """窗口启动行为分析器"""
    
    def __init__(self):
        self.position_log = []
        self.timing_log = []
        self.start_time = time.time()
    
    def log_position(self, root, event_name):
        """记录窗口位置"""
        try:
            geometry = root.geometry()
            current_time = time.time() - self.start_time
            
            # 解析几何信息
            if '+' in geometry:
                size_part = geometry.split('+')[0]
                pos_parts = geometry.split('+')[1:]
                position = f"+{'+'.join(pos_parts)}"
            else:
                size_part = geometry
                position = "未知"
            
            log_entry = {
                'time': current_time,
                'event': event_name,
                'geometry': geometry,
                'size': size_part,
                'position': position,
                'visible': root.winfo_viewable(),
                'mapped': root.winfo_ismapped()
            }
            
            self.position_log.append(log_entry)
            print(f"[{current_time:.3f}s] {event_name}: {geometry} (可见:{root.winfo_viewable()}, 映射:{root.winfo_ismapped()})")
            
        except Exception as e:
            print(f"记录位置失败: {str(e)}")
    
    def log_timing(self, event_name):
        """记录时间点"""
        current_time = time.time() - self.start_time
        self.timing_log.append({
            'time': current_time,
            'event': event_name
        })
        print(f"[{current_time:.3f}s] 时间点: {event_name}")


def test_tkinter_default_behavior():
    """测试Tkinter默认行为"""
    print("测试1: Tkinter默认窗口行为")
    print("=" * 50)
    
    analyzer = WindowStartupAnalyzer()
    
    # 创建窗口
    analyzer.log_timing("开始创建窗口")
    root = tk.Tk()
    analyzer.log_position(root, "窗口创建后")
    
    # 设置标题
    analyzer.log_timing("设置标题")
    root.title("Tkinter默认行为测试")
    analyzer.log_position(root, "设置标题后")
    
    # 设置几何信息
    analyzer.log_timing("设置几何信息")
    root.geometry("920x600")
    analyzer.log_position(root, "设置几何信息后")
    
    # 更新窗口
    analyzer.log_timing("更新窗口")
    root.update_idletasks()
    analyzer.log_position(root, "update_idletasks后")
    
    # 显示窗口
    analyzer.log_timing("显示窗口")
    root.deiconify()
    analyzer.log_position(root, "deiconify后")
    
    # 延迟检查
    def delayed_check():
        time.sleep(0.1)
        analyzer.log_position(root, "延迟0.1秒后")
        time.sleep(0.5)
        analyzer.log_position(root, "延迟0.6秒后")
        root.after(100, root.destroy)
    
    threading.Thread(target=delayed_check, daemon=True).start()
    
    # 运行主循环
    analyzer.log_timing("开始主循环")
    root.mainloop()
    
    return analyzer


def test_window_manager_behavior():
    """测试窗口管理器行为"""
    print(f"\n{'='*50}")
    print("测试2: 窗口管理器行为")
    print("=" * 50)
    
    analyzer = WindowStartupAnalyzer()
    
    # 创建窗口
    analyzer.log_timing("开始创建窗口")
    root = tk.Tk()
    analyzer.log_position(root, "窗口创建后")
    
    # 使用窗口管理器
    analyzer.log_timing("开始窗口管理器设置")
    wm = WindowManager()
    analyzer.log_position(root, "创建窗口管理器后")
    
    # 分步执行窗口管理器的操作
    analyzer.log_timing("加载配置")
    config = wm.load_window_config()
    window_config = config.get("main_window", wm.default_config["main_window"])
    analyzer.log_position(root, "加载配置后")
    
    analyzer.log_timing("设置标题")
    root.title("窗口管理器行为测试")
    analyzer.log_position(root, "设置标题后")
    
    analyzer.log_timing("获取窗口尺寸")
    width = window_config["width"]
    height = window_config["height"]
    analyzer.log_position(root, "获取尺寸后")
    
    analyzer.log_timing("计算居中位置")
    # 这里是关键：update_idletasks可能导致窗口显示
    root.update_idletasks()
    analyzer.log_position(root, "update_idletasks后")
    
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    analyzer.log_position(root, "计算位置后")
    
    analyzer.log_timing("应用几何设置")
    root.geometry(f"{width}x{height}+{x}+{y}")
    analyzer.log_position(root, "应用几何设置后")
    
    # 延迟检查
    def delayed_check():
        time.sleep(0.1)
        analyzer.log_position(root, "延迟0.1秒后")
        time.sleep(0.5)
        analyzer.log_position(root, "延迟0.6秒后")
        root.after(100, root.destroy)
    
    threading.Thread(target=delayed_check, daemon=True).start()
    
    # 运行主循环
    analyzer.log_timing("开始主循环")
    root.mainloop()
    
    return analyzer


def test_optimized_behavior():
    """测试优化后的行为"""
    print(f"\n{'='*50}")
    print("测试3: 优化后的窗口行为")
    print("=" * 50)
    
    analyzer = WindowStartupAnalyzer()
    
    # 创建窗口但不显示
    analyzer.log_timing("开始创建窗口")
    root = tk.Tk()
    analyzer.log_position(root, "窗口创建后")
    
    # 立即隐藏窗口
    analyzer.log_timing("隐藏窗口")
    root.withdraw()
    analyzer.log_position(root, "withdraw后")
    
    # 设置所有属性
    analyzer.log_timing("设置标题")
    root.title("优化后的窗口行为测试")
    analyzer.log_position(root, "设置标题后")
    
    analyzer.log_timing("计算居中位置")
    width, height = 920, 600
    
    # 不调用update_idletasks，直接获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    analyzer.log_position(root, "计算位置后")
    
    analyzer.log_timing("设置几何信息")
    root.geometry(f"{width}x{height}+{x}+{y}")
    analyzer.log_position(root, "设置几何信息后")
    
    analyzer.log_timing("显示窗口")
    root.deiconify()
    analyzer.log_position(root, "deiconify后")
    
    # 延迟检查
    def delayed_check():
        time.sleep(0.1)
        analyzer.log_position(root, "延迟0.1秒后")
        time.sleep(0.5)
        analyzer.log_position(root, "延迟0.6秒后")
        root.after(100, root.destroy)
    
    threading.Thread(target=delayed_check, daemon=True).start()
    
    # 运行主循环
    analyzer.log_timing("开始主循环")
    root.mainloop()
    
    return analyzer


def analyze_results(analyzers):
    """分析测试结果"""
    print(f"\n{'='*60}")
    print("📊 窗口启动行为分析结果")
    print("=" * 60)
    
    for i, (name, analyzer) in enumerate(analyzers, 1):
        print(f"\n测试{i}: {name}")
        print("-" * 40)
        
        # 分析位置变化
        positions = []
        for log in analyzer.position_log:
            if log['position'] != "未知":
                positions.append(log['position'])
        
        unique_positions = list(dict.fromkeys(positions))  # 保持顺序的去重
        
        print(f"位置变化序列: {' -> '.join(unique_positions)}")
        
        # 查找关键事件
        key_events = []
        for log in analyzer.position_log:
            if log['event'] in ['窗口创建后', 'update_idletasks后', '应用几何设置后', 'deiconify后']:
                key_events.append(f"{log['event']}: {log['position']}")
        
        print("关键事件:")
        for event in key_events:
            print(f"  • {event}")
        
        # 检查是否有位置跳跃
        if len(unique_positions) > 1:
            print(f"⚠️ 发现位置跳跃: {len(unique_positions)}个不同位置")
        else:
            print("✅ 位置稳定，无跳跃")


def main():
    """主分析函数"""
    print("窗口启动位置变化深度分析")
    print("=" * 60)
    print("分析目标: 查找窗口先在左侧显示然后居中的原因")
    print("=" * 60)
    
    analyzers = []
    
    # 测试1: Tkinter默认行为
    try:
        analyzer1 = test_tkinter_default_behavior()
        analyzers.append(("Tkinter默认行为", analyzer1))
    except Exception as e:
        print(f"测试1失败: {str(e)}")
    
    # 测试2: 窗口管理器行为
    try:
        analyzer2 = test_window_manager_behavior()
        analyzers.append(("窗口管理器行为", analyzer2))
    except Exception as e:
        print(f"测试2失败: {str(e)}")
    
    # 测试3: 优化后的行为
    try:
        analyzer3 = test_optimized_behavior()
        analyzers.append(("优化后的行为", analyzer3))
    except Exception as e:
        print(f"测试3失败: {str(e)}")
    
    # 分析结果
    if analyzers:
        analyze_results(analyzers)
        
        print(f"\n{'='*60}")
        print("💡 问题原因分析")
        print("=" * 60)
        print("根据测试结果，窗口位置跳跃的可能原因:")
        print("1. root.update_idletasks() 调用时机过早")
        print("2. 窗口在设置geometry之前就已经显示")
        print("3. Tkinter的默认显示机制")
        print("4. 操作系统的窗口管理器行为")
        
        print(f"\n💡 解决方案建议:")
        print("1. 在窗口创建后立即调用 root.withdraw() 隐藏窗口")
        print("2. 设置完所有属性后再调用 root.deiconify() 显示窗口")
        print("3. 避免在设置geometry之前调用 update_idletasks()")
        print("4. 使用延迟显示机制")
    
    else:
        print("❌ 所有测试都失败了")


if __name__ == "__main__":
    main()
