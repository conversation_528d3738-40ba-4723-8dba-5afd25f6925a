#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涨跌停价格获取修复测试脚本
测试新的 get_instrument_detail 接口是否能正确获取涨跌停价格
"""

import sys
import os

# 添加项目路径到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

try:
    from xtquant import xtdata
    print("✅ 成功导入 xtdata 模块")
except ImportError as e:
    print(f"❌ 导入 xtdata 模块失败: {e}")
    print("请确保QMT已正确安装并配置")
    sys.exit(1)

def test_get_instrument_detail(stock_code):
    """测试 get_instrument_detail 接口获取涨跌停价格"""
    print(f"\n测试股票: {stock_code}")
    print("-" * 50)
    
    try:
        # 测试新的接口
        print("📊 使用 get_instrument_detail 获取涨跌停价格...")
        detail = xtdata.get_instrument_detail(stock_code)
        
        if detail:
            print(f"✅ 成功获取股票详情")
            
            # 获取涨跌停价格
            up_stop_price = detail.get('UpStopPrice', 0)
            down_stop_price = detail.get('DownStopPrice', 0)
            
            print(f"📈 涨停价: {up_stop_price}")
            print(f"📉 跌停价: {down_stop_price}")
            
            # 获取其他相关信息
            instrument_name = detail.get('InstrumentName', 'N/A')
            last_close = detail.get('LastClose', 0)
            
            print(f"📝 股票名称: {instrument_name}")
            print(f"💰 前收盘价: {last_close}")
            
            # 验证涨跌停价格是否有效
            if up_stop_price > 0 and down_stop_price > 0:
                print(f"✅ 涨跌停价格获取成功")
                
                # 计算涨跌幅
                if last_close > 0:
                    up_percent = ((up_stop_price - last_close) / last_close) * 100
                    down_percent = ((down_stop_price - last_close) / last_close) * 100
                    print(f"📊 涨幅: {up_percent:.2f}%")
                    print(f"📊 跌幅: {down_percent:.2f}%")
                
                return True
            else:
                print(f"⚠️ 涨跌停价格无效")
                return False
        else:
            print(f"❌ 无法获取股票详情")
            return False
            
    except Exception as e:
        print(f"❌ 获取股票详情失败: {str(e)}")
        return False

def test_get_full_tick(stock_code):
    """测试原有的 get_full_tick 接口"""
    print(f"\n📊 使用 get_full_tick 获取行情数据...")
    
    try:
        market_data = xtdata.get_full_tick([stock_code])
        
        if market_data and stock_code in market_data:
            data = market_data[stock_code]
            print(f"✅ 成功获取行情数据")
            
            # 获取基本价格信息
            last_price = data.get('lastPrice', 0)
            last_close = data.get('lastClose', 0)
            
            print(f"💰 最新价: {last_price}")
            print(f"💰 前收盘: {last_close}")
            
            # 尝试获取涨跌停价格（应该失败）
            upper_limit = (data.get('upperLimit') or 
                         data.get('upLimit') or 
                         data.get('limitUp') or 
                         data.get('UpStopPrice') or 
                         0)
                         
            lower_limit = (data.get('lowerLimit') or 
                         data.get('downLimit') or 
                         data.get('limitDown') or 
                         data.get('DownStopPrice') or 
                         0)
            
            print(f"📈 涨停价(从tick): {upper_limit}")
            print(f"📉 跌停价(从tick): {lower_limit}")
            
            if upper_limit > 0 and lower_limit > 0:
                print(f"✅ 从tick数据获取到涨跌停价格")
                return True
            else:
                print(f"⚠️ 从tick数据无法获取涨跌停价格")
                return False
        else:
            print(f"❌ 无法获取行情数据")
            return False
            
    except Exception as e:
        print(f"❌ 获取行情数据失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 涨跌停价格获取修复测试")
    print("=" * 60)
    
    # 测试股票列表
    test_stocks = [
        "603859.SH",  # 主板股票
        "002901.SZ",  # 深市主板
        "300830.SZ",  # 创业板
        "688001.SH",  # 科创板（如果有的话）
    ]
    
    results = {}
    
    for stock_code in test_stocks:
        print(f"\n{'='*60}")
        print(f"🧪 测试股票: {stock_code}")
        print(f"{'='*60}")
        
        # 测试新接口
        detail_success = test_get_instrument_detail(stock_code)
        
        # 测试原接口
        tick_success = test_get_full_tick(stock_code)
        
        results[stock_code] = {
            'detail_success': detail_success,
            'tick_success': tick_success
        }
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print(f"{'='*60}")
    
    for stock_code, result in results.items():
        print(f"\n📈 {stock_code}:")
        print(f"   get_instrument_detail: {'✅ 成功' if result['detail_success'] else '❌ 失败'}")
        print(f"   get_full_tick:         {'✅ 成功' if result['tick_success'] else '❌ 失败'}")
    
    # 统计成功率
    detail_success_count = sum(1 for r in results.values() if r['detail_success'])
    tick_success_count = sum(1 for r in results.values() if r['tick_success'])
    total_count = len(results)
    
    print(f"\n📊 成功率统计:")
    print(f"   get_instrument_detail: {detail_success_count}/{total_count} ({detail_success_count/total_count*100:.1f}%)")
    print(f"   get_full_tick:         {tick_success_count}/{total_count} ({tick_success_count/total_count*100:.1f}%)")
    
    if detail_success_count > tick_success_count:
        print(f"\n✅ 修复成功！新接口比原接口获取涨跌停价格的成功率更高")
    elif detail_success_count == tick_success_count and detail_success_count > 0:
        print(f"\n✅ 修复有效！新接口与原接口成功率相同")
    else:
        print(f"\n⚠️ 需要进一步调试，新接口成功率未达到预期")

if __name__ == "__main__":
    main()
