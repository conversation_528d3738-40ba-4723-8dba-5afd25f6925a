#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一窗口管理模式验证测试
验证所有窗口设置都使用统一的窗口管理器
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from window_manager import (
    setup_main_window, 
    setup_test_window, 
    center_existing_window, 
    reset_window_to_default,
    get_default_window_size,
    WindowManager
)


def test_unified_window_functions():
    """测试统一窗口管理函数"""
    print("🔧 测试统一窗口管理函数")
    print("=" * 50)
    
    # 测试便捷函数
    functions_to_test = [
        ("setup_main_window", setup_main_window),
        ("setup_test_window", setup_test_window),
        ("center_existing_window", center_existing_window),
        ("reset_window_to_default", reset_window_to_default),
        ("get_default_window_size", get_default_window_size)
    ]
    
    success_count = 0
    for name, func in functions_to_test:
        try:
            if name == "get_default_window_size":
                result = func()
                print(f"✅ {name}: 可调用，返回 {result}")
            else:
                print(f"✅ {name}: 函数可导入")
            success_count += 1
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {str(e)}")
    
    print(f"\n📊 便捷函数测试: {success_count}/{len(functions_to_test)} 成功")
    return success_count == len(functions_to_test)


def test_window_size_consistency():
    """测试窗口尺寸一致性"""
    print(f"\n{'='*50}")
    print("📏 测试窗口尺寸一致性")
    print("=" * 50)
    
    # 获取默认尺寸
    main_width, main_height = get_default_window_size("main_window")
    test_width, test_height = get_default_window_size("test_window")
    
    print(f"主窗口默认尺寸: {main_width}x{main_height}")
    print(f"测试窗口默认尺寸: {test_width}x{test_height}")
    
    # 检查主窗口是否为标准尺寸
    if main_width == 920 and main_height == 600:
        print("✅ 主窗口尺寸符合标准 (920x600)")
        main_ok = True
    else:
        print(f"❌ 主窗口尺寸不标准，期望 920x600，实际 {main_width}x{main_height}")
        main_ok = False
    
    # 检查测试窗口尺寸
    if test_width == 600 and test_height == 400:
        print("✅ 测试窗口尺寸符合标准 (600x400)")
        test_ok = True
    else:
        print(f"❌ 测试窗口尺寸不标准，期望 600x400，实际 {test_width}x{test_height}")
        test_ok = False
    
    return main_ok and test_ok


def create_unified_test_window():
    """创建统一窗口管理测试界面"""
    print(f"\n{'='*50}")
    print("🎨 创建统一窗口管理测试界面")
    print("=" * 50)
    
    root = tk.Tk()
    
    # 使用统一的窗口设置
    setup_main_window(root, "统一窗口管理模式测试")
    
    # 创建主界面
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="统一窗口管理模式测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 当前窗口信息
    info_frame = ttk.LabelFrame(main_frame, text="当前窗口信息", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    geometry_var = tk.StringVar()
    status_var = tk.StringVar()
    
    ttk.Label(info_frame, text="窗口几何:").grid(row=0, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=geometry_var, font=("Courier", 10)).grid(row=0, column=1, sticky=tk.W)
    
    ttk.Label(info_frame, text="管理状态:").grid(row=1, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=status_var, font=("Courier", 10)).grid(row=1, column=1, sticky=tk.W)
    
    def update_info():
        geometry = root.geometry()
        geometry_var.set(geometry)
        
        # 检查是否使用标准尺寸
        if geometry.startswith('920x600'):
            status_var.set("✅ 使用统一管理 (920x600)")
        else:
            status_var.set(f"⚠️ 非标准尺寸 ({geometry.split('+')[0]})")
        
        root.after(1000, update_info)
    
    update_info()
    
    # 功能测试按钮
    test_frame = ttk.LabelFrame(main_frame, text="统一管理功能测试", padding=10)
    test_frame.pack(fill=tk.X, pady=(0, 20))
    
    # 第一行按钮
    row1_frame = ttk.Frame(test_frame)
    row1_frame.pack(fill=tk.X, pady=(0, 10))
    
    def test_center():
        center_existing_window(root)
        messagebox.showinfo("测试", "已使用统一居中函数")
    
    def test_reset():
        reset_window_to_default(root, "main_window", "统一窗口管理模式测试")
        messagebox.showinfo("测试", "已使用统一重置函数")
    
    def test_new_window():
        new_root = tk.Tk()
        setup_test_window(new_root, "统一管理测试窗口")
        
        # 简单的测试窗口内容
        frame = ttk.Frame(new_root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="这是使用统一管理的测试窗口", font=("Arial", 12)).pack(pady=20)
        ttk.Button(frame, text="关闭", command=new_root.destroy).pack()
    
    ttk.Button(row1_frame, text="统一居中", command=test_center).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row1_frame, text="统一重置", command=test_reset).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row1_frame, text="新测试窗口", command=test_new_window).pack(side=tk.LEFT, padx=(0, 10))
    
    # 第二行按钮
    row2_frame = ttk.Frame(test_frame)
    row2_frame.pack(fill=tk.X)
    
    def show_config():
        wm = WindowManager()
        config = wm.get_window_config("main_window")
        config_text = "\n".join([f"{k}: {v}" for k, v in config.items()])
        messagebox.showinfo("窗口配置", f"主窗口配置:\n\n{config_text}")
    
    def show_sizes():
        main_size = get_default_window_size("main_window")
        test_size = get_default_window_size("test_window")
        messagebox.showinfo("默认尺寸", f"主窗口: {main_size[0]}x{main_size[1]}\n测试窗口: {test_size[0]}x{test_size[1]}")
    
    ttk.Button(row2_frame, text="查看配置", command=show_config).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row2_frame, text="查看尺寸", command=show_sizes).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row2_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT)
    
    # 说明文本
    help_frame = ttk.LabelFrame(main_frame, text="统一管理说明", padding=10)
    help_frame.pack(fill=tk.BOTH, expand=True)
    
    help_text = tk.Text(help_frame, wrap=tk.WORD, height=10)
    help_text.pack(fill=tk.BOTH, expand=True)
    help_text.insert(tk.END, """
统一窗口管理模式说明:

1. 统一函数:
   • setup_main_window() - 设置主窗口
   • setup_test_window() - 设置测试窗口
   • center_existing_window() - 居中现有窗口
   • reset_window_to_default() - 重置到默认设置
   • get_default_window_size() - 获取默认尺寸

2. 统一尺寸:
   • 主窗口: 920x600 像素
   • 测试窗口: 600x400 像素
   • 所有窗口自动居中显示

3. 统一行为:
   • 自动位置记忆
   • 智能居中显示
   • 配置化管理
   • 跨平台兼容

4. 使用方式:
   • 所有新窗口都使用便捷函数创建
   • 不再使用 root.geometry() 直接设置
   • 统一通过窗口管理器处理所有窗口操作

5. 优势:
   • 代码一致性
   • 维护简单
   • 用户体验统一
   • 功能扩展方便
    """)
    help_text.config(state=tk.DISABLED)
    
    return root


def main():
    """主测试函数"""
    print("🔧 统一窗口管理模式验证测试")
    print("=" * 60)
    
    results = []
    
    # 测试统一函数
    results.append(("统一函数测试", test_unified_window_functions()))
    
    # 测试尺寸一致性
    results.append(("尺寸一致性测试", test_window_size_consistency()))
    
    # 创建测试界面
    print(f"\n{'='*50}")
    print("🎨 创建统一管理测试界面...")
    test_window = create_unified_test_window()
    
    # 显示测试结果
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("🎉 统一窗口管理模式验证成功！")
        print("📝 请在GUI中测试各项统一管理功能")
    else:
        print("⚠️ 部分测试未通过，请检查配置")
    
    # 运行测试界面
    test_window.mainloop()
    
    print(f"\n{'='*60}")
    print("✅ 统一窗口管理模式测试完成")


if __name__ == "__main__":
    main()
