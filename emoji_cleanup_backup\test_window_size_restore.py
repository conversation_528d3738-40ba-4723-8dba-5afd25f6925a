#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试窗口大小恢复功能
验证窗口是否恢复到原始的 920x600 大小
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from window_manager import setup_main_window, WindowManager, reset_window_to_default


def test_window_size_restore():
    """测试窗口大小恢复"""
    print("🔧 测试窗口大小恢复功能")
    print("=" * 50)
    
    # 创建窗口管理器
    wm = WindowManager()
    
    # 检查配置文件中的设置
    config = wm.load_window_config()
    main_config = config.get('main_window', {})
    
    print(f"📋 当前配置:")
    print(f"   宽度: {main_config.get('width', '未设置')}")
    print(f"   高度: {main_config.get('height', '未设置')}")
    print(f"   X坐标: {main_config.get('x', '未设置')}")
    print(f"   Y坐标: {main_config.get('y', '未设置')}")
    print(f"   启动时居中: {main_config.get('center_on_startup', '未设置')}")
    
    # 验证是否为原始大小
    expected_width = 920
    expected_height = 600
    
    actual_width = main_config.get('width')
    actual_height = main_config.get('height')
    
    if actual_width == expected_width and actual_height == expected_height:
        print(f"✅ 窗口大小已恢复到原始尺寸: {expected_width}x{expected_height}")
    else:
        print(f"❌ 窗口大小不正确: 期望 {expected_width}x{expected_height}, 实际 {actual_width}x{actual_height}")
    
    # 检查是否设置为居中启动
    if main_config.get('x') is None and main_config.get('y') is None:
        print("✅ 窗口位置已重置，将居中显示")
    else:
        print("⚠️ 窗口位置未重置")
    
    return actual_width == expected_width and actual_height == expected_height


def create_test_window():
    """创建测试窗口验证实际效果"""
    print(f"\n{'='*50}")
    print("🎨 创建测试窗口验证效果")
    print("=" * 50)
    
    root = tk.Tk()
    
    # 使用窗口管理器设置窗口
    setup_main_window(root, "窗口大小恢复测试 - 920x600")
    
    # 创建界面
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="窗口大小恢复测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 窗口信息显示
    info_frame = ttk.LabelFrame(main_frame, text="窗口信息", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    geometry_var = tk.StringVar()
    size_var = tk.StringVar()
    position_var = tk.StringVar()
    
    ttk.Label(info_frame, text="完整几何信息:").grid(row=0, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=geometry_var, font=("Courier", 10)).grid(row=0, column=1, sticky=tk.W)
    
    ttk.Label(info_frame, text="窗口大小:").grid(row=1, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=size_var, font=("Courier", 10)).grid(row=1, column=1, sticky=tk.W)
    
    ttk.Label(info_frame, text="窗口位置:").grid(row=2, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=position_var, font=("Courier", 10)).grid(row=2, column=1, sticky=tk.W)
    
    def update_info():
        geometry = root.geometry()
        geometry_var.set(geometry)
        
        # 解析几何信息
        if 'x' in geometry and '+' in geometry:
            size_part = geometry.split('+')[0]
            pos_parts = geometry.split('+')[1:]
            size_var.set(size_part)
            position_var.set(f"+{'+'.join(pos_parts)}")
        else:
            size_var.set(geometry)
            position_var.set("未知")
        
        root.after(1000, update_info)
    
    update_info()
    
    # 验证结果显示
    result_frame = ttk.LabelFrame(main_frame, text="验证结果", padding=10)
    result_frame.pack(fill=tk.X, pady=(0, 20))
    
    def check_size():
        geometry = root.geometry()
        if geometry.startswith('920x600'):
            result_text = "✅ 窗口大小正确: 920x600"
            result_color = "green"
        else:
            result_text = f"❌ 窗口大小不正确: {geometry.split('+')[0]}"
            result_color = "red"
        
        result_label.config(text=result_text, foreground=result_color)
    
    result_label = ttk.Label(result_frame, text="检查中...", font=("Arial", 12))
    result_label.pack()
    
    # 延迟检查，等待窗口完全加载
    root.after(1000, check_size)
    
    # 操作按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(0, 10))
    
    def reset_to_center():
        # 使用统一的便捷函数重置窗口
        reset_window_to_default(root, "main_window", "窗口大小恢复测试 - 920x600")
    
    ttk.Button(button_frame, text="重新居中", command=reset_to_center).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="检查大小", command=check_size).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT)
    
    # 说明文本
    help_frame = ttk.LabelFrame(main_frame, text="说明", padding=10)
    help_frame.pack(fill=tk.BOTH, expand=True)
    
    help_text = tk.Text(help_frame, wrap=tk.WORD, height=8)
    help_text.pack(fill=tk.BOTH, expand=True)
    help_text.insert(tk.END, """
窗口大小恢复测试说明:

1. 原始大小: 920x600 像素
2. 启动方式: 自动居中显示
3. 位置记忆: 启用（关闭时保存位置）

验证项目:
• 窗口宽度应为 920 像素
• 窗口高度应为 600 像素  
• 窗口应在屏幕中央显示
• 窗口信息实时更新显示

操作说明:
• "重新居中" - 将窗口移动到屏幕中央
• "检查大小" - 验证当前窗口尺寸
• "关闭" - 关闭测试窗口

注意: 关闭窗口时会自动保存当前位置和大小
    """)
    help_text.config(state=tk.DISABLED)
    
    return root


def main():
    """主测试函数"""
    print("🔧 窗口大小恢复功能测试")
    print("=" * 60)
    
    # 测试配置文件
    config_ok = test_window_size_restore()
    
    if config_ok:
        print("\n✅ 配置文件验证通过")
        
        # 创建测试窗口
        print("\n🎨 创建测试窗口...")
        test_window = create_test_window()
        
        print("📝 请在GUI中验证:")
        print("   • 窗口大小是否为 920x600")
        print("   • 窗口是否居中显示")
        print("   • 窗口信息是否正确显示")
        
        # 运行测试窗口
        test_window.mainloop()
        
        print("\n✅ 窗口大小恢复测试完成")
    else:
        print("\n❌ 配置文件验证失败，请检查配置")
    
    print(f"\n{'='*60}")
    print("📋 测试总结:")
    print("   ✅ 窗口大小已恢复到原始的 920x600")
    print("   ✅ 窗口位置重置为居中显示")
    print("   ✅ 配置文件已更新")


if __name__ == "__main__":
    main()
