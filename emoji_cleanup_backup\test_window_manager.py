#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口管理器功能测试
验证窗口位置管理、居中显示、位置记忆等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from window_manager import WindowManager, setup_main_window, setup_test_window, center_existing_window, reset_window_to_default


def test_window_manager_basic():
    """测试窗口管理器基本功能"""
    print("🔧 测试窗口管理器基本功能")
    print("=" * 50)
    
    # 创建窗口管理器实例
    wm = WindowManager()
    
    # 测试配置加载
    config = wm.load_window_config()
    print(f"✅ 配置加载成功: {len(config)} 个窗口类型")
    
    # 测试配置保存
    test_config = config.copy()
    test_config["test_window"] = {
        "width": 800,
        "height": 500,
        "x": 200,
        "y": 100
    }
    wm.save_window_config(test_config)
    print("✅ 配置保存成功")
    
    # 验证配置
    loaded_config = wm.load_window_config()
    if "test_window" in loaded_config:
        print("✅ 配置验证成功")
    else:
        print("❌ 配置验证失败")


def test_window_positioning():
    """测试窗口定位功能"""
    print(f"\n{'='*50}")
    print("🎯 测试窗口定位功能")
    print("=" * 50)
    
    def create_test_window(window_type, title, test_func=None):
        """创建测试窗口"""
        root = tk.Tk()
        
        # 创建窗口管理器
        wm = WindowManager()
        
        # 设置窗口
        wm.setup_window(root, window_type, title)
        
        # 创建界面
        main_frame = ttk.Frame(root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text=title, font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 窗口信息
        info_frame = ttk.LabelFrame(main_frame, text="窗口信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        geometry_var = tk.StringVar()
        ttk.Label(info_frame, text="当前位置:").pack(anchor=tk.W)
        geometry_label = ttk.Label(info_frame, textvariable=geometry_var, font=("Courier", 10))
        geometry_label.pack(anchor=tk.W)
        
        def update_geometry():
            geometry_var.set(root.geometry())
            root.after(1000, update_geometry)
        
        update_geometry()
        
        # 测试按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        def center_window():
            # 使用统一的便捷函数
            center_existing_window(root)
        
        def save_position():
            wm.save_window_position(root, window_type)
            messagebox.showinfo("成功", "窗口位置已保存")
        
        ttk.Button(button_frame, text="居中显示", command=center_window).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="保存位置", command=save_position).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT)
        
        # 说明文本
        help_text = tk.Text(main_frame, height=8, wrap=tk.WORD)
        help_text.pack(fill=tk.BOTH, expand=True)
        help_text.insert(tk.END, f"""
窗口类型: {window_type}

功能说明:
• 拖动窗口到不同位置测试位置记忆
• 点击"居中显示"测试居中功能
• 点击"保存位置"手动保存当前位置
• 关闭窗口时会自动保存位置
• 重新打开窗口会恢复到上次位置

测试步骤:
1. 拖动窗口到屏幕不同位置
2. 关闭窗口
3. 重新运行程序验证位置恢复
        """)
        help_text.config(state=tk.DISABLED)
        
        if test_func:
            test_func(root, wm)
        
        return root
    
    # 创建主窗口测试
    print("📱 创建主窗口测试...")
    main_window = create_test_window("main_window", "主窗口位置测试")
    
    # 创建测试窗口
    print("📱 创建测试窗口...")
    test_window = create_test_window("test_window", "测试窗口位置测试")
    
    print("✅ 窗口创建完成")
    print("📝 请手动测试窗口拖动和位置记忆功能")
    
    # 运行主窗口
    main_window.mainloop()


def test_convenience_functions():
    """测试便捷函数"""
    print(f"\n{'='*50}")
    print("🚀 测试便捷函数")
    print("=" * 50)
    
    def test_main_window():
        root = tk.Tk()
        setup_main_window(root, "便捷函数测试 - 主窗口")
        
        frame = ttk.Frame(root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="这是使用便捷函数创建的主窗口", font=("Arial", 12)).pack(pady=20)
        ttk.Button(frame, text="关闭", command=root.destroy).pack()
        
        return root
    
    def test_test_window():
        root = tk.Tk()
        setup_test_window(root, "便捷函数测试 - 测试窗口")
        
        frame = ttk.Frame(root, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="这是使用便捷函数创建的测试窗口", font=("Arial", 12)).pack(pady=20)
        ttk.Button(frame, text="关闭", command=root.destroy).pack()
        
        return root
    
    # 测试便捷函数
    print("📱 测试主窗口便捷函数...")
    main_win = test_main_window()
    
    print("📱 测试测试窗口便捷函数...")
    test_win = test_test_window()
    
    print("✅ 便捷函数测试完成")
    
    # 运行窗口
    main_win.mainloop()


def test_config_management():
    """测试配置管理功能"""
    print(f"\n{'='*50}")
    print("⚙️ 测试配置管理功能")
    print("=" * 50)
    
    wm = WindowManager()
    
    # 测试获取配置
    config = wm.get_window_config("main_window")
    print(f"✅ 主窗口配置: {config}")
    
    # 测试更新配置
    wm.update_window_config("main_window", width=1000, height=700, center_on_startup=True)
    updated_config = wm.get_window_config("main_window")
    print(f"✅ 更新后配置: {updated_config}")
    
    # 测试重置配置
    wm.reset_window_config("main_window")
    reset_config = wm.get_window_config("main_window")
    print(f"✅ 重置后配置: {reset_config}")
    
    print("✅ 配置管理测试完成")


def create_comprehensive_test():
    """创建综合测试窗口"""
    print(f"\n{'='*50}")
    print("🔬 综合功能测试")
    print("=" * 50)
    
    root = tk.Tk()
    wm = WindowManager()
    wm.setup_window(root, "main_window", "窗口管理器综合测试")
    
    # 创建主界面
    main_frame = ttk.Frame(root, padding=10)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="窗口管理器综合测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 当前窗口信息
    info_frame = ttk.LabelFrame(main_frame, text="当前窗口信息", padding=10)
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    geometry_var = tk.StringVar()
    config_var = tk.StringVar()
    
    ttk.Label(info_frame, text="几何信息:").grid(row=0, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=geometry_var, font=("Courier", 10)).grid(row=0, column=1, sticky=tk.W)
    
    ttk.Label(info_frame, text="配置信息:").grid(row=1, column=0, sticky=tk.W)
    ttk.Label(info_frame, textvariable=config_var, font=("Courier", 10)).grid(row=1, column=1, sticky=tk.W)
    
    def update_info():
        geometry_var.set(root.geometry())
        config = wm.get_window_config("main_window")
        config_var.set(f"W:{config.get('width')} H:{config.get('height')} X:{config.get('x')} Y:{config.get('y')}")
        root.after(1000, update_info)
    
    update_info()
    
    # 测试按钮区域
    test_frame = ttk.LabelFrame(main_frame, text="功能测试", padding=10)
    test_frame.pack(fill=tk.X, pady=(0, 20))
    
    # 第一行按钮
    row1_frame = ttk.Frame(test_frame)
    row1_frame.pack(fill=tk.X, pady=(0, 10))
    
    def center_window():
        x, y = wm.center_window(root, root.winfo_width(), root.winfo_height())
        root.geometry(f"+{x}+{y}")
    
    def save_position():
        wm.save_window_position(root, "main_window")
        messagebox.showinfo("成功", "位置已保存")
    
    def reset_config():
        wm.reset_window_config("main_window")
        messagebox.showinfo("成功", "配置已重置")
    
    ttk.Button(row1_frame, text="居中显示", command=center_window).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row1_frame, text="保存位置", command=save_position).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row1_frame, text="重置配置", command=reset_config).pack(side=tk.LEFT, padx=(0, 10))
    
    # 第二行按钮
    row2_frame = ttk.Frame(test_frame)
    row2_frame.pack(fill=tk.X)
    
    def set_size_small():
        root.geometry("600x400")
    
    def set_size_medium():
        root.geometry("800x600")
    
    def set_size_large():
        root.geometry("1200x800")
    
    ttk.Button(row2_frame, text="小尺寸", command=set_size_small).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row2_frame, text="中尺寸", command=set_size_medium).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(row2_frame, text="大尺寸", command=set_size_large).pack(side=tk.LEFT, padx=(0, 10))
    
    # 说明文本
    help_frame = ttk.LabelFrame(main_frame, text="使用说明", padding=10)
    help_frame.pack(fill=tk.BOTH, expand=True)
    
    help_text = tk.Text(help_frame, wrap=tk.WORD)
    help_text.pack(fill=tk.BOTH, expand=True)
    help_text.insert(tk.END, """
窗口管理器功能测试说明:

1. 位置管理:
   • 拖动窗口到不同位置
   • 关闭程序重新打开，窗口会恢复到上次位置
   • 点击"居中显示"可以将窗口居中

2. 尺寸管理:
   • 拖动窗口边缘调整大小
   • 使用"小/中/大尺寸"按钮快速调整
   • 窗口大小会被记住

3. 配置管理:
   • "保存位置"手动保存当前位置
   • "重置配置"恢复默认设置
   • 配置信息实时显示在上方

4. 测试步骤:
   a) 调整窗口位置和大小
   b) 关闭程序
   c) 重新运行验证设置是否保存
   d) 测试居中和重置功能

注意: 窗口关闭时会自动保存当前位置和大小
    """)
    help_text.config(state=tk.DISABLED)
    
    return root


def main():
    """主测试函数"""
    print("🔧 窗口管理器功能测试")
    print("=" * 60)
    
    # 基本功能测试
    test_window_manager_basic()
    
    # 配置管理测试
    test_config_management()
    
    # 创建综合测试窗口
    test_window = create_comprehensive_test()
    
    print(f"\n{'='*60}")
    print("✅ 测试准备完成")
    print("📝 请在GUI中测试各项功能:")
    print("   • 拖动窗口测试位置记忆")
    print("   • 调整窗口大小测试尺寸记忆")
    print("   • 使用按钮测试各项功能")
    print("   • 关闭重开验证设置保存")
    
    # 运行测试窗口
    test_window.mainloop()
    
    print(f"\n{'='*60}")
    print("✅ 窗口管理器测试完成")


if __name__ == "__main__":
    main()
